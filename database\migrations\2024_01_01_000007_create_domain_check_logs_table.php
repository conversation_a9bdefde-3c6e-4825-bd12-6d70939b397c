<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 域名检查日志表迁移
 */
return new class extends Migration
{
    /**
     * 运行迁移
     */
    public function up(): void
    {
        Schema::create('domain_check_logs', function (Blueprint $table) {
            $table->id();
            
            // 关联域名池
            $table->unsignedBigInteger('domain_pool_id')->comment('域名池ID');
            $table->foreign('domain_pool_id')->references('id')->on('domain_pools')->onDelete('cascade');
            
            // 检查结果
            $table->tinyInteger('status')->comment('状态：1-正常，2-异常，3-封禁，4-维护');
            $table->integer('health_score')->default(0)->comment('健康分数(0-100)');
            $table->json('check_result')->nullable()->comment('检查结果详情');
            $table->string('check_type', 20)->default('basic')->comment('检查类型');
            $table->decimal('response_time', 8, 2)->default(0)->comment('响应时间(毫秒)');
            $table->text('error_message')->nullable()->comment('错误信息');
            
            $table->timestamps();
            
            // 索引
            $table->index(['domain_pool_id', 'created_at']);
            $table->index(['status', 'created_at']);
            $table->index(['check_type', 'created_at']);
            $table->index('health_score');
        });
    }

    /**
     * 回滚迁移
     */
    public function down(): void
    {
        Schema::dropIfExists('domain_check_logs');
    }
};