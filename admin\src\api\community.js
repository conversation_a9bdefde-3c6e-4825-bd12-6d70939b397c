import request from '@/utils/request'
import { mockCommunityAPI } from './mock/community.js'

// 检查是否使用模拟数据
// 在开发环境中，如果后端服务器未运行，自动启用Mock模式
const useMock = import.meta.env.VITE_ENABLE_MOCK === 'true' || import.meta.env.DEV

// 获取群组列表
export function getGroupList(params) {
  if (useMock) {
    return mockCommunityAPI.getGroupList(params)
  }
  return request({
    url: '/admin/groups',
    method: 'get',
    params
  })
}

// 获取群组列表（别名，用于模版管理）
export function getGroups(params) {
  return getGroupList(params)
}

// 获取群组统计
export function getGroupStats() {
  if (useMock) {
    return mockCommunityAPI.getGroupStats()
  }
  return request({
    url: '/admin/groups/stats',
    method: 'get'
  })
}

// 获取群组详情
export function getGroupDetail(id) {
  if (useMock) {
    return mockCommunityAPI.getGroupDetail(id)
  }
  return request({
    url: `/admin/groups/${id}`,
    method: 'get'
  })
}

// 创建群组
export function createGroup(data) {
  if (useMock) {
    return mockCommunityAPI.createGroup(data)
  }
  return request({
    url: '/admin/groups',
    method: 'post',
    data
  })
}

// 获取城市列表
export function getCityList() {
  if (useMock) {
    return mockCommunityAPI.getCityList()
  }
  return request({
    url: '/admin/cities',
    method: 'get'
  })
}

// 根据坐标获取城市信息
export function getCityByCoordinates(lat, lng) {
  if (useMock) {
    return mockCommunityAPI.getCityByCoordinates(lat, lng)
  }
  return request({
    url: '/admin/location/city',
    method: 'get',
    params: { lat, lng }
  })
}

// 生成推广链接
export function generatePromotionLink(groupId, config) {
  if (useMock) {
    return mockCommunityAPI.generatePromotionLink(groupId, config)
  }
  return request({
    url: `/admin/groups/${groupId}/promotion-link`,
    method: 'post',
    data: config
  })
}

// 生成落地页
export function generateLandingPage(groupId, config) {
  if (useMock) {
    return mockCommunityAPI.generateLandingPage(groupId, config)
  }
  return request({
    url: `/admin/groups/${groupId}/landing-page`,
    method: 'post',
    data: config
  })
}

// 更新群组
export function updateGroup(id, data) {
  return request({
    url: `/admin/groups/${id}`,
    method: 'put',
    data
  })
}

// 删除群组
export function deleteGroup(id) {
  if (useMock) {
    return mockCommunityAPI.deleteGroup(id)
  }
  return request({
    url: `/admin/groups/${id}`,
    method: 'delete'
  })
}

// 批量删除群组
export function batchDeleteGroups(ids) {
  if (useMock) {
    return mockCommunityAPI.batchDeleteGroups(ids)
  }
  return request({
    url: '/admin/groups/batch-delete',
    method: 'post',
    data: { ids }
  })
}

// 更新群组状态
export function updateGroupStatus(id, status) {
  if (useMock) {
    return mockCommunityAPI.updateGroupStatus(id, status)
  }
  return request({
    url: `/admin/groups/${id}/status`,
    method: 'put',
    data: { status }
  })
}

// 获取群组成员
export function getGroupMembers(id, params) {
  return request({
    url: `/admin/groups/${id}/members`,
    method: 'get',
    params
  })
}

// 添加群组成员
export function addGroupMember(id, data) {
  return request({
    url: `/admin/groups/${id}/members`,
    method: 'post',
    data
  })
}

// 移除群组成员
export function removeGroupMember(id, memberId) {
  return request({
    url: `/admin/groups/${id}/members/${memberId}`,
    method: 'delete'
  })
}

// 获取群组分析数据
export function getGroupAnalytics(id, params) {
  return request({
    url: `/admin/groups/${id}/analytics`,
    method: 'get',
    params
  })
}

// 生成群组二维码
export function generateGroupQRCode(id) {
  return request({
    url: `/admin/groups/${id}/qrcode`,
    method: 'post'
  })
}

// 克隆群组
export function cloneGroup(id, data) {
  return request({
    url: `/admin/groups/${id}/clone`,
    method: 'post',
    data
  })
}

// 导出群组数据
export function exportGroups(params) {
  if (useMock) {
    return mockCommunityAPI.exportGroups(params)
  }
  return request({
    url: '/admin/groups/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// ========== 模板管理相关API ==========

// 获取模板列表
export function getTemplates(params) {
  if (useMock) {
    return mockCommunityAPI.getTemplates(params)
  }
  return request({
    url: '/admin/group-templates',
    method: 'get',
    params
  })
}

// 获取模板详情
export function getTemplate(id) {
  if (useMock) {
    return mockCommunityAPI.getTemplate(id)
  }
  return request({
    url: `/admin/group-templates/${id}`,
    method: 'get'
  })
}

// 创建模板
export function createTemplate(data) {
  if (useMock) {
    return mockCommunityAPI.createTemplate(data)
  }
  return request({
    url: '/admin/group-templates',
    method: 'post',
    data
  })
}

// 更新模板
export function updateTemplate(id, data) {
  if (useMock) {
    return mockCommunityAPI.updateTemplate(id, data)
  }
  return request({
    url: `/admin/group-templates/${id}`,
    method: 'put',
    data
  })
}

// 删除模板
export function deleteTemplate(id) {
  if (useMock) {
    return mockCommunityAPI.deleteTemplate(id)
  }
  return request({
    url: `/admin/group-templates/${id}`,
    method: 'delete'
  })
}

// 获取模板分类
export function getTemplateCategories() {
  if (useMock) {
    return mockCommunityAPI.getTemplateCategories()
  }
  return request({
    url: '/admin/group-templates/categories',
    method: 'get'
  })
}

// 复制模板
export function copyTemplate(id) {
  if (useMock) {
    return mockCommunityAPI.copyTemplate(id)
  }
  return request({
    url: `/admin/group-templates/${id}/copy`,
    method: 'post'
  })
}

// 切换模板状态
export function toggleTemplateStatus(id, status) {
  if (useMock) {
    return mockCommunityAPI.toggleTemplateStatus(id, status)
  }
  return request({
    url: `/admin/group-templates/${id}/status`,
    method: 'put',
    data: { is_active: status }
  })
}

// 批量更新群组状态
export function batchUpdateGroupStatus(groupIds, status) {
  return request({
    url: '/admin/groups/batch-status',
    method: 'put',
    data: { group_ids: groupIds, status }
  })
}

// 应用群组模板（保留这个因为有特殊用途）
export function applyGroupTemplate(templateId, data) {
  return request({
    url: `/admin/group-templates/${templateId}/apply`,
    method: 'post',
    data
  })
}

// 批量操作模板
export function batchOperateTemplates(action, templateIds) {
  if (useMock) {
    return mockCommunityAPI.batchOperateTemplates(action, templateIds)
  }
  return request({
    url: `/admin/group-templates/batch-operation`,
    method: 'post',
    data: { action, template_ids: templateIds }
  })
}

// 预览模板
export function previewTemplate(id, variables = {}) {
  if (useMock) {
    return mockCommunityAPI.previewTemplate(id, variables)
  }
  return request({
    url: `/admin/group-templates/${id}/preview`,
    method: 'get',
    params: variables
  })
}

// 获取模板统计
export function getTemplateStatistics() {
  if (useMock) {
    return mockCommunityAPI.getTemplateStatistics()
  }
  return request({
    url: `/admin/group-templates/statistics`,
    method: 'get'
  })
}

// ========== 以下是为解决 "No matching export" 错误新增的函数 ==========

// 获取群组内容
export function getGroupContent(groupId, params) {
  if (useMock) {
    return mockCommunityAPI.getGroupContent(groupId, params)
  }
  return request({
    url: `/admin/groups/${groupId}/content`,
    method: 'get',
    params
  })
}

// 更新群组内容
export function updateGroupContent(contentId, data) {
  if (useMock) {
    return mockCommunityAPI.updateGroupContent(contentId, data)
  }
  return request({
    url: `/admin/content/${contentId}`,
    method: 'put',
    data
  })
}

// 更新群组二维码
export function updateGroupQrCode(groupId, qrCodeUrl) {
    if (useMock) {
        return mockCommunityAPI.updateGroupQrCode(groupId, qrCodeUrl);
    }
    return request({
        url: `/admin/groups/${groupId}/qrcode`,
        method: 'put',
        data: { qrcode: qrCodeUrl },
    });
}

// 获取自动化规则
export function getRules(params) {
  if (useMock) {
    return mockCommunityAPI.getRules(params)
  }
  return request({
    url: '/admin/community/rules',
    method: 'get',
    params
  })
}

// 创建自动化规则
export function createRule(data) {
  if (useMock) {
    return mockCommunityAPI.createRule(data)
  }
  return request({
    url: '/admin/community/rules',
    method: 'post',
    data
  })
}

// 更新自动化规则
export function updateRule(id, data) {
  if (useMock) {
    return mockCommunityAPI.updateRule(id, data)
  }
  return request({
    url: `/admin/community/rules/${id}`,
    method: 'put',
    data
  })
}

// 删除自动化规则
export function deleteRule(id) {
  if (useMock) {
    return mockCommunityAPI.deleteRule(id)
  }
  return request({
    url: `/admin/community/rules/${id}`,
    method: 'delete'
  })
}
