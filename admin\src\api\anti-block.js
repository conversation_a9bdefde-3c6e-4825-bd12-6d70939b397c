import api from './index'

// 检查是否使用模拟数据
const useMock = import.meta.env.VITE_ENABLE_MOCK === 'true' ||
                (import.meta.env.DEV && !import.meta.env.VITE_API_BASE_URL)

// 防屏蔽系统相关API
export const getAntiBlockStats = () => {
  if (useMock) {
    console.log('🎯 使用Mock数据: 反屏蔽统计')

    return Promise.resolve({
      data: {
        domain_stats: {
          total: 156,
          active: 142,
          abnormal: 8,
          blocked: 6
        },
        link_stats: {
          total: 2345,
          active: 2198,
          today_created: 45,
          today_clicks: 1234
        },
        system_health: {
          score: 91.0,
          status: 'good',
          uptime: 99.8
        },
        today_stats: {
          checks: 1234,
          blocks: 89,
          success_rate: 92.8
        },
        monthly_stats: {
          total_checks: 45678,
          total_blocks: 3456,
          block_rate: 7.6
        },
        recent_blocks: [
          { domain: 'example1.com', blocked_at: '2024-12-19 10:30:00', reason: '内容违规' },
          { domain: 'example2.com', blocked_at: '2024-12-19 09:15:00', reason: '域名被墙' },
          { domain: 'example3.com', blocked_at: '2024-12-19 08:45:00', reason: '服务器异常' }
        ]
      }
    })
  }

  return api.get('/admin/anti-block/stats')
}

export const getDomains = (params) => {
  if (useMock) {
    console.log('🎯 使用Mock数据: 域名列表', { params })

    const mockDomains = [
      {
        id: 1,
        domain: 'main.example.com',
        status: 'active',
        type: 'primary',
        ssl_status: 'valid',
        last_check: '2024-12-19 10:30:00',
        response_time: 156,
        uptime: 99.8,
        created_at: '2024-01-15 10:00:00'
      },
      {
        id: 2,
        domain: 'backup1.example.com',
        status: 'active',
        type: 'backup',
        ssl_status: 'valid',
        last_check: '2024-12-19 10:25:00',
        response_time: 203,
        uptime: 98.5,
        created_at: '2024-02-20 14:30:00'
      },
      {
        id: 3,
        domain: 'backup2.example.com',
        status: 'blocked',
        type: 'backup',
        ssl_status: 'expired',
        last_check: '2024-12-19 10:20:00',
        response_time: null,
        uptime: 0,
        created_at: '2024-03-10 09:15:00'
      },
      {
        id: 4,
        domain: 'cdn.example.com',
        status: 'active',
        type: 'cdn',
        ssl_status: 'valid',
        last_check: '2024-12-19 10:28:00',
        response_time: 89,
        uptime: 99.9,
        created_at: '2024-04-05 16:45:00'
      }
    ]

    const page = params?.page || 1
    const limit = params?.limit || 10
    const start = (page - 1) * limit
    const end = start + limit
    const paginatedDomains = mockDomains.slice(start, end)

    return Promise.resolve({
      data: {
        code: 200,
        message: '获取成功',
        data: {
          list: paginatedDomains,
          total: mockDomains.length,
          page: page,
          limit: limit,
          pages: Math.ceil(mockDomains.length / limit)
        }
      }
    })
  }

  return api.get('/admin/anti-block/domains', { params })
}

export const getDomainList = (params) => {
  // 复用getDomains的逻辑
  return getDomains(params)
}

export const addDomain = (data) => {
  if (useMock) {
    console.log('🎯 使用Mock数据: 添加域名', { data })

    return Promise.resolve({
      data: {
        code: 200,
        message: '域名添加成功',
        data: {
          id: Date.now(),
          domain: data.domain,
          status: 'active',
          type: data.type || 'backup',
          ssl_status: 'pending',
          last_check: new Date().toISOString(),
          response_time: null,
          uptime: 0,
          created_at: new Date().toISOString()
        }
      }
    })
  }

  return api.post('/admin/anti-block/domains', data)
}

export const updateDomain = (id, data) => {
  if (useMock) {
    console.log('🎯 使用Mock数据: 更新域名', { id, data })

    return Promise.resolve({
      data: {
        code: 200,
        message: '域名更新成功',
        data: {
          id: id,
          ...data,
          updated_at: new Date().toISOString()
        }
      }
    })
  }

  return api.put(`/admin/anti-block/domains/${id}`, data)
}

export const deleteDomain = (id) => {
  if (useMock) {
    console.log('🎯 使用Mock数据: 删除域名', { id })

    return Promise.resolve({
      data: {
        code: 200,
        message: '域名删除成功',
        data: { id: id }
      }
    })
  }

  return api.delete(`/admin/anti-block/domains/${id}`)
}

export const checkDomains = (data) => {
  if (useMock) {
    console.log('🎯 使用Mock数据: 批量检查域名', { data })

    const results = data.domain_ids.map(id => ({
      id,
      domain: `domain${id}.example.com`,
      status: Math.random() > 0.2 ? 'active' : 'blocked',
      response_time: Math.random() > 0.2 ? Math.floor(Math.random() * 300) + 50 : null,
      ssl_status: Math.random() > 0.1 ? 'valid' : 'expired',
      checked_at: new Date().toISOString()
    }))

    return Promise.resolve({
      data: {
        code: 200,
        message: '批量检查完成',
        data: {
          results,
          total: results.length,
          success: results.filter(r => r.status === 'active').length,
          failed: results.filter(r => r.status === 'blocked').length
        }
      }
    })
  }

  return api.post('/admin/anti-block/domains/batch-check', data)
}

export const batchDeleteDomains = (data) => {
  if (useMock) {
    console.log('🎯 使用Mock数据: 批量删除域名', { data })

    return Promise.resolve({
      data: {
        code: 200,
        message: `成功删除 ${data.domain_ids.length} 个域名`,
        data: {
          deleted_count: data.domain_ids.length,
          deleted_ids: data.domain_ids
        }
      }
    })
  }

  return api.post('/admin/anti-block/domains/batch-delete', data)
}

// 短链接管理
export const getShortLinks = (params) => {
  if (useMock) {
    console.log('🎯 使用Mock数据: 短链接列表', { params })

    const mockShortLinks = [
      {
        id: 1,
        short_url: 'https://s.ly/abc123',
        original_url: 'https://main.example.com/register',
        title: '注册页面',
        status: 'active',
        clicks: 1234,
        created_at: '2024-12-15 10:00:00',
        expires_at: '2025-12-15 10:00:00'
      },
      {
        id: 2,
        short_url: 'https://s.ly/def456',
        original_url: 'https://main.example.com/download',
        title: '下载页面',
        status: 'active',
        clicks: 567,
        created_at: '2024-12-16 14:30:00',
        expires_at: '2025-12-16 14:30:00'
      },
      {
        id: 3,
        short_url: 'https://s.ly/ghi789',
        original_url: 'https://backup1.example.com/promo',
        title: '推广页面',
        status: 'blocked',
        clicks: 89,
        created_at: '2024-12-17 09:15:00',
        expires_at: '2025-12-17 09:15:00'
      }
    ]

    const page = params?.page || 1
    const limit = params?.limit || 10
    const start = (page - 1) * limit
    const end = start + limit
    const paginatedLinks = mockShortLinks.slice(start, end)

    return Promise.resolve({
      data: {
        code: 200,
        message: '获取成功',
        data: {
          list: paginatedLinks,
          total: mockShortLinks.length,
          page: page,
          limit: limit,
          pages: Math.ceil(mockShortLinks.length / limit)
        }
      }
    })
  }

  return api.get('/admin/anti-block/short-links', { params })
}

export const createShortLink = (data) => {
  if (useMock) {
    console.log('🎯 使用Mock数据: 创建短链接', { data })

    const shortCode = Math.random().toString(36).substring(2, 8)

    return Promise.resolve({
      data: {
        code: 200,
        message: '短链接创建成功',
        data: {
          id: Date.now(),
          short_url: `https://s.ly/${shortCode}`,
          original_url: data.original_url,
          title: data.title || '未命名链接',
          status: 'active',
          clicks: 0,
          created_at: new Date().toISOString(),
          expires_at: data.expires_at || new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString()
        }
      }
    })
  }

  return api.post('/admin/anti-block/short-links', data)
}

export const deleteShortLink = (id) => {
  if (useMock) {
    console.log('🎯 使用Mock数据: 删除短链接', { id })

    return Promise.resolve({
      data: {
        code: 200,
        message: '短链接删除成功',
        data: { id: id }
      }
    })
  }

  return api.delete(`/admin/anti-block/short-links/${id}`)
}

export const batchDeleteShortLinks = (data) => {
  if (useMock) {
    console.log('🎯 使用Mock数据: 批量删除短链接', { data })

    return Promise.resolve({
      data: {
        code: 200,
        message: `成功删除 ${data.link_ids.length} 个短链接`,
        data: {
          deleted_count: data.link_ids.length,
          deleted_ids: data.link_ids
        }
      }
    })
  }

  return api.post('/admin/anti-block/short-links/batch-delete', data)
}

export const exportShortLinks = (params) => {
  if (useMock) {
    console.log('🎯 使用Mock数据: 导出短链接', { params })

    return Promise.resolve({
      data: {
        code: 200,
        message: '导出成功',
        data: {
          download_url: '/mock/short-links-export.xlsx',
          filename: `short-links-${new Date().toISOString().split('T')[0]}.xlsx`,
          total_records: 156
        }
      }
    })
  }

  return api.get('/admin/anti-block/short-links/export', { params })
}

// 统计分析
export const getAccessStats = (params) => {
  if (useMock) {
    console.log('🎯 使用Mock数据: 访问统计', { params })

    return Promise.resolve({
      data: {
        total_clicks: 45678,
        unique_visitors: 12345,
        success_rate: 95.8,
        avg_response_time: 1250,
        click_trend: 12.5,
        visitor_trend: 8.3,
        success_trend: 2.1,
        response_trend: -150
      }
    })
  }

  return api.get('/admin/anti-block/access-stats', { params })
}

export const getAccessLogs = (params) => {
  return api.get('/admin/anti-block/access-logs', { params })
}

export const getClickTrends = (params) => {
  if (useMock) {
    console.log('🎯 使用Mock数据: 点击趋势', { params })

    return Promise.resolve({
      data: {
        data: Array.from({ length: 7 }, (_, i) => ({
          date: new Date(Date.now() - (6 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          clicks: Math.floor(Math.random() * 2000) + 1000,
          unique_visitors: Math.floor(Math.random() * 800) + 400,
          success_rate: Math.floor(Math.random() * 10) + 90
        }))
      }
    })
  }

  return api.get('/admin/anti-block/click-trends', { params })
}

export const getRegionStats = (params) => {
  return api.get('/admin/anti-block/region-stats', { params })
}

export const getPlatformStats = (params) => {
  return api.get('/admin/anti-block/platform-stats', { params })
}

export const generateQRCode = (data) => {
  return api.post('/admin/anti-block/qrcode', data)
}

export const updateShortLink = (id, data) => {
  return api.put(`/admin/anti-block/short-links/${id}`, data)
}

export const switchShortLinkDomain = (id, data) => {
  return api.post(`/admin/anti-block/short-links/${id}/switch-domain`, data)
}

// 群组推广链接相关API
export const generateGroupPromotionLink = (groupId, config) => {
  return api.post(`/admin/groups/${groupId}/promotion-link`, config)
}

export const getGroupPromotionLinks = (groupId, params) => {
  return api.get(`/admin/groups/${groupId}/promotion-links`, { params })
}

export const updatePromotionLinkConfig = (groupId, config) => {
  return api.put(`/admin/groups/${groupId}/anti-block-config`, config)
}

// ==================== 防红分析页面专用API ====================

/**
 * 获取防红系统分析数据
 */
export const getAnalyticsData = (params) => {
  if (useMock) {
    console.log('🎯 使用Mock数据: 防红分析数据', { params })

    return Promise.resolve({
      data: {
        success: true,
        data: {
          // 系统概览
          system_overview: {
            overall_health: 85,
            active_domains: 12,
            total_domains: 15,
            total_access: 128965,
            today_access: 5432,
            avg_response_time: 245
          },
          
          // 域名统计
          domain_stats: {
            healthy: 12,
            warning: 2,
            blocked: 1
          },

          // 性能指标
          performance_metrics: {
            avg_response_time: 245,
            success_rate: 98.5,
            error_rate: 1.5,
            concurrent_connections: 156
          },

          // 安全指标
          security_metrics: {
            security_score: 92,
            threat_level: 'low',
            suspicious_access: 23,
            blocked_attacks: 45
          },

          // 告警信息
          alerts: [
            {
              id: 1,
              level: 'warning',
              title: '域名响应时间过长',
              message: 'example.com 响应时间超过3秒',
              timestamp: new Date().toISOString()
            },
            {
              id: 2,
              level: 'critical',
              title: '域名检测失败',
              message: 'test.com 连续3次检测失败',
              timestamp: new Date(Date.now() - 300000).toISOString()
            }
          ],

          // 最近访问日志
          recent_logs: Array.from({ length: 20 }, (_, i) => ({
            id: i + 1,
            visitor_ip: `192.168.1.${100 + i}`,
            platform: ['wechat', 'qq', 'browser', 'mobile'][Math.floor(Math.random() * 4)],
            location: ['北京', '上海', '广州', '深圳', '杭州'][Math.floor(Math.random() * 5)],
            access_time: new Date(Date.now() - i * 60000).toISOString(),
            is_valid: Math.random() > 0.1
          })),

          // 详细统计表格数据
          table_data: Array.from({ length: 50 }, (_, i) => ({
            id: i + 1,
            domain: `domain${i + 1}.example.com`,
            total_access: Math.floor(Math.random() * 10000) + 1000,
            success_rate: Math.floor(Math.random() * 20) + 80,
            avg_response_time: Math.floor(Math.random() * 1000) + 200,
            status: [1, 1, 1, 2, 3][Math.floor(Math.random() * 5)],
            last_check_time: new Date(Date.now() - Math.random() * 86400000).toISOString()
          }))
        }
      }
    })
  }

  return api.get('/admin/anti-block/analytics', { params })
}

/**
 * 获取实时监控数据
 */
export const getRealTimeMonitor = () => {
  if (useMock) {
    console.log('🎯 使用Mock数据: 实时监控数据')

    return Promise.resolve({
      data: {
        success: true,
        data: {
          overall_health: Math.floor(Math.random() * 20) + 80,
          metrics: {
            system_status: {
              overall_health: Math.floor(Math.random() * 20) + 80,
              active_domains: Math.floor(Math.random() * 5) + 10,
              today_access_count: Math.floor(Math.random() * 1000) + 5000,
              domain_availability_rate: Math.floor(Math.random() * 10) + 90,
              today_success_rate: Math.floor(Math.random() * 10) + 90
            },
            performance_metrics: {
              response_time: {
                average: Math.floor(Math.random() * 500) + 200
              }
            },
            error_analytics: {
              error_recovery_rate: Math.random() * 0.05
            },
            security_indicators: {
              threat_level: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)],
              suspicious_access: Math.floor(Math.random() * 50),
              high_frequency_ips: Math.floor(Math.random() * 10),
              blocked_domains: Math.floor(Math.random() * 3),
              security_score: Math.floor(Math.random() * 20) + 80
            },
            domain_health: {
              health_trend: Array.from({ length: 24 }, () => Math.floor(Math.random() * 20) + 80)
            },
            access_patterns: {
              hourly_pattern: Array.from({ length: 24 }, () => Math.floor(Math.random() * 1000)),
              platform_distribution: {
                wechat: Math.floor(Math.random() * 500) + 200,
                qq: Math.floor(Math.random() * 300) + 100,
                browser: Math.floor(Math.random() * 400) + 150,
                mobile: Math.floor(Math.random() * 200) + 50
              }
            }
          },
          alerts: [
            {
              id: 'alert_' + Date.now(),
              level: 'warning',
              title: '域名响应时间异常',
              message: '检测到域名响应时间超过阈值',
              timestamp: new Date().toISOString()
            }
          ],
          recommendations: [
            {
              category: 'performance',
              priority: 'medium',
              title: '优化响应性能',
              description: '建议启用CDN加速以提升访问速度',
              actions: ['启用CDN', '优化服务器配置']
            }
          ]
        }
      }
    })
  }

  return api.get('/admin/anti-block/real-time-monitor')
}

/**
 * 获取域名状态
 */
export const getDomainStatus = () => {
  if (useMock) {
    console.log('🎯 使用Mock数据: 域名状态')

    return Promise.resolve({
      data: {
        success: true,
        data: Array.from({ length: 15 }, (_, i) => ({
          id: i + 1,
          domain: `domain${i + 1}.example.com`,
          status: [1, 1, 1, 1, 1, 1, 2, 2, 3][Math.floor(Math.random() * 9)],
          health_score: Math.floor(Math.random() * 30) + 70
        }))
      }
    })
  }

  return api.get('/admin/anti-block/domain-status')
}

/**
 * 获取最近访问日志
 */
export const getRecentAccessLogs = (params) => {
  if (useMock) {
    console.log('🎯 使用Mock数据: 最近访问日志', { params })

    const limit = params?.limit || 50

    return Promise.resolve({
      data: {
        success: true,
        data: Array.from({ length: limit }, (_, i) => ({
          id: i + 1,
          visitor_ip: `192.168.1.${100 + i}`,
          platform: ['wechat', 'qq', 'browser', 'mobile'][Math.floor(Math.random() * 4)],
          location: ['北京', '上海', '广州', '深圳', '杭州'][Math.floor(Math.random() * 5)],
          access_time: new Date(Date.now() - i * 60000).toISOString(),
          user_agent: 'Mozilla/5.0 (compatible; MockBot/1.0)',
          is_valid: Math.random() > 0.1
        }))
      }
    })
  }

  return api.get('/admin/anti-block/recent-access-logs', { params })
}

/**
 * 检测单个域名
 */
export const checkDomain = (domainId) => {
  if (useMock) {
    console.log('🎯 使用Mock数据: 检测域名', { domainId })

    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          data: {
            success: true,
            data: {
              id: domainId,
              status: Math.random() > 0.2 ? 1 : 2,
              response_time: Math.floor(Math.random() * 500) + 100,
              checked_at: new Date().toISOString()
            }
          }
        })
      }, 1000)
    })
  }

  return api.post(`/admin/anti-block/domains/${domainId}/check`)
}

/**
 * 批量检测所有域名
 */
export const checkAllDomains = () => {
  if (useMock) {
    console.log('🎯 使用Mock数据: 批量检测域名')

    return Promise.resolve({
      data: {
        success: true,
        message: '批量检测任务已启动'
      }
    })
  }

  return api.post('/admin/anti-block/domains/check-all')
}

/**
 * 导出分析报告
 */
export const exportAnalyticsReport = (params) => {
  if (useMock) {
    console.log('🎯 使用Mock数据: 导出分析报告', { params })

    return Promise.resolve({
      data: {
        success: true,
        data: {
          download_url: '/mock/analytics-report.xlsx',
          filename: `analytics-report-${new Date().toISOString().split('T')[0]}.xlsx`
        }
      }
    })
  }

  return api.get('/admin/anti-block/analytics/export', { params })
}

// ==================== 防红分析相关的导出对象 ====================
export const antiBlockApi = {
  // 基础API
  getAntiBlockStats,
  getDomains,
  getDomainList,
  addDomain,
  updateDomain,
  deleteDomain,
  checkDomains,
  batchDeleteDomains,
  
  // 短链接API
  getShortLinks,
  createShortLink,
  deleteShortLink,
  batchDeleteShortLinks,
  exportShortLinks,
  updateShortLink,
  switchShortLinkDomain,
  
  // 统计分析API
  getAccessStats,
  getAccessLogs,
  getClickTrends,
  getRegionStats,
  getPlatformStats,
  generateQRCode,
  
  // 群组推广API
  generateGroupPromotionLink,
  getGroupPromotionLinks,
  updatePromotionLinkConfig,
  
  // 防红分析专用API
  getAnalyticsData,
  getRealTimeMonitor,
  getDomainStatus,
  getRecentAccessLogs,
  checkDomain,
  checkAllDomains,
  exportAnalyticsReport
}
