<template>
  <div class="api-test-page">
    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <span>API测试工具</span>
          <el-tag type="info">调试工具</el-tag>
        </div>
      </template>

      <div class="test-section">
        <h3>推广链接API测试</h3>
        
        <el-form :model="testForm" label-width="120px">
          <el-form-item label="群组ID">
            <el-input v-model="testForm.groupId" placeholder="输入群组ID" />
          </el-form-item>
          <el-form-item label="配置参数">
            <el-input 
              v-model="testForm.configJson" 
              type="textarea" 
              :rows="4"
              placeholder='{"enable_anti_block": true, "enable_short_link": true}'
            />
          </el-form-item>
        </el-form>

        <div class="test-actions">
          <el-button type="primary" @click="testPromotionLinkAPI" :loading="loading">
            测试推广链接API (POST)
          </el-button>
          <el-button type="success" @click="testSimpleAPI" :loading="loading">
            测试简单API (GET)
          </el-button>
          <el-button @click="clearResults">清空结果</el-button>
        </div>
      </div>

      <div class="test-section" v-if="results.length > 0">
        <h3>测试结果</h3>
        <div class="results-container">
          <div v-for="(result, index) in results" :key="index" class="result-item">
            <div class="result-header">
              <span class="result-time">{{ result.timestamp }}</span>
              <el-tag :type="result.success ? 'success' : 'danger'">
                {{ result.success ? '成功' : '失败' }}
              </el-tag>
            </div>
            
            <div class="result-content">
              <h4>请求信息</h4>
              <pre>{{ JSON.stringify(result.request, null, 2) }}</pre>
              
              <h4>响应信息</h4>
              <pre>{{ JSON.stringify(result.response, null, 2) }}</pre>
              
              <h4 v-if="result.error">错误信息</h4>
              <pre v-if="result.error" class="error-text">{{ result.error }}</pre>
            </div>
          </div>
        </div>
      </div>

      <div class="test-section">
        <h3>快速测试用例</h3>
        <div class="quick-tests">
          <el-button @click="quickTest1">测试群组1</el-button>
          <el-button @click="quickTest2">测试群组2</el-button>
          <el-button @click="quickTestInvalid">测试无效群组</el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { generateGroupPromotionLink } from '@/api/anti-block'
import request from '@/utils/request'

// 测试表单
const testForm = reactive({
  groupId: '1',
  configJson: JSON.stringify({
    enable_anti_block: true,
    enable_short_link: true,
    link_type: 'promotion'
  }, null, 2)
})

// 状态
const loading = ref(false)
const results = ref([])

// 测试推广链接API
const testPromotionLinkAPI = async () => {
  if (!testForm.groupId) {
    ElMessage.warning('请输入群组ID')
    return
  }

  loading.value = true
  
  try {
    let config = {}
    try {
      config = JSON.parse(testForm.configJson)
    } catch (e) {
      ElMessage.error('配置参数JSON格式错误')
      loading.value = false
      return
    }

    const startTime = Date.now()
    
    // 记录请求信息
    const requestInfo = {
      url: `/api/admin/groups/${testForm.groupId}/promotion-link`,
      method: 'POST',
      data: config,
      groupId: testForm.groupId
    }

    console.log('🚀 发送API请求:', requestInfo)

    const result = await generateGroupPromotionLink(testForm.groupId, config)
    
    const endTime = Date.now()
    const duration = endTime - startTime

    console.log('✅ API响应成功:', result)

    // 记录成功结果
    results.value.unshift({
      timestamp: new Date().toLocaleString(),
      success: true,
      duration: `${duration}ms`,
      request: requestInfo,
      response: result,
      error: null
    })

    ElMessage.success(`API调用成功 (${duration}ms)`)

  } catch (error) {
    console.error('❌ API调用失败:', error)

    // 记录失败结果
    results.value.unshift({
      timestamp: new Date().toLocaleString(),
      success: false,
      request: {
        url: `/api/admin/groups/${testForm.groupId}/promotion-link`,
        method: 'POST',
        data: JSON.parse(testForm.configJson),
        groupId: testForm.groupId
      },
      response: error.response?.data || null,
      error: {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data
      }
    })

    ElMessage.error(`API调用失败: ${error.message}`)
  } finally {
    loading.value = false
  }
}

// 测试简单API
const testSimpleAPI = async () => {
  if (!testForm.groupId) {
    ElMessage.warning('请输入群组ID')
    return
  }

  loading.value = true

  try {
    const startTime = Date.now()

    // 记录请求信息
    const requestInfo = {
      url: `/admin/test/promotion-link/${testForm.groupId}`,
      method: 'GET',
      groupId: testForm.groupId
    }

    console.log('🚀 发送简单API请求:', requestInfo)

    const result = await request.get(`/admin/test/promotion-link/${testForm.groupId}`)

    const endTime = Date.now()
    const duration = endTime - startTime

    console.log('✅ 简单API响应成功:', result)

    // 记录成功结果
    results.value.unshift({
      timestamp: new Date().toLocaleString(),
      success: true,
      duration: `${duration}ms`,
      request: requestInfo,
      response: result,
      error: null
    })

    ElMessage.success(`简单API调用成功 (${duration}ms)`)

  } catch (error) {
    console.error('❌ 简单API调用失败:', error)

    // 记录失败结果
    results.value.unshift({
      timestamp: new Date().toLocaleString(),
      success: false,
      request: {
        url: `/admin/test/promotion-link/${testForm.groupId}`,
        method: 'GET',
        groupId: testForm.groupId
      },
      response: error.response?.data || null,
      error: {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data
      }
    })

    ElMessage.error(`简单API调用失败: ${error.message}`)
  } finally {
    loading.value = false
  }
}

// 清空结果
const clearResults = () => {
  results.value = []
  ElMessage.info('结果已清空')
}

// 快速测试用例
const quickTest1 = () => {
  testForm.groupId = '1'
  testForm.configJson = JSON.stringify({
    enable_anti_block: true,
    enable_short_link: true,
    link_type: 'promotion'
  }, null, 2)
  testPromotionLinkAPI()
}

const quickTest2 = () => {
  testForm.groupId = '2'
  testForm.configJson = JSON.stringify({
    enable_anti_block: false,
    enable_short_link: true,
    source: 'wechat'
  }, null, 2)
  testPromotionLinkAPI()
}

const quickTestInvalid = () => {
  testForm.groupId = '99999'
  testForm.configJson = JSON.stringify({
    enable_anti_block: true,
    enable_short_link: true
  }, null, 2)
  testPromotionLinkAPI()
}
</script>

<style scoped>
.api-test-page {
  padding: 20px;
}

.test-card {
  max-width: 1000px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.test-section {
  margin-bottom: 30px;
}

.test-section h3 {
  margin-bottom: 15px;
  color: #303133;
}

.test-actions {
  margin: 20px 0;
}

.test-actions .el-button {
  margin-right: 10px;
}

.results-container {
  max-height: 600px;
  overflow-y: auto;
}

.result-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  margin-bottom: 15px;
  padding: 15px;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.result-time {
  font-size: 12px;
  color: #909399;
}

.result-content h4 {
  margin: 15px 0 8px 0;
  color: #606266;
  font-size: 14px;
}

.result-content pre {
  background: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.error-text {
  background: #fef0f0 !important;
  color: #f56c6c;
}

.quick-tests {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}
</style>
