<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 微信群组表迁移
 */
return new class extends Migration
{
    /**
     * 运行迁移
     */
    public function up(): void
    {
        Schema::create('wechat_groups', function (Blueprint $table) {
            $table->id();
            
            // 基础信息
            $table->string('title', 200)->comment('群组名称');
            $table->string('slug', 250)->unique()->comment('唯一标识');
            $table->string('category', 50)->comment('群组分类');
            $table->decimal('price', 8, 2)->default(0)->comment('群组价格');
            $table->integer('member_limit')->default(500)->comment('成员上限');
            $table->text('description')->comment('群组描述');
            $table->string('cover_image', 500)->nullable()->comment('群组头像');
            $table->string('qr_code', 500)->nullable()->comment('群组二维码');
            $table->enum('status', ['active', 'inactive', 'draft'])->default('draft')->comment('状态');
            
            // 创建者信息
            $table->unsignedBigInteger('creator_id')->comment('创建者ID');
            $table->foreign('creator_id')->references('id')->on('users')->onDelete('cascade');
            
            // 城市定位配置
            $table->boolean('city_location')->default(false)->comment('启用城市定位');
            $table->string('city_insert_strategy', 20)->default('auto')->comment('城市插入策略');
            $table->string('title_template', 200)->nullable()->comment('标题模板');
            
            // 富媒体内容 (JSON)
            $table->json('rich_content')->nullable()->comment('富媒体内容');
            
            // 营销配置 (JSON)
            $table->json('marketing_config')->nullable()->comment('营销配置');
            
            // 支付配置 (JSON)
            $table->json('payment_config')->nullable()->comment('支付配置');
            
            // 分销配置 (JSON)
            $table->json('distribution_config')->nullable()->comment('分销配置');
            
            // 防红配置 (JSON)
            $table->json('anti_block_config')->nullable()->comment('防红配置');
            
            // 统计数据
            $table->integer('view_count')->default(0)->comment('浏览次数');
            $table->integer('member_count')->default(0)->comment('成员数量');
            $table->integer('order_count')->default(0)->comment('订单数量');
            $table->decimal('total_revenue', 10, 2)->default(0)->comment('总收入');
            
            // 排序权重
            $table->integer('sort_order')->default(0)->comment('排序权重');
            
            // 时间戳
            $table->timestamps();
            $table->softDeletes();
            
            // 索引
            $table->index(['status', 'created_at']);
            $table->index(['category', 'status']);
            $table->index(['creator_id', 'status']);
            $table->index('sort_order');
            // SQLite 不支持全文索引，使用普通索引替代
            // $table->fullText(['title', 'description']);
            $table->index('title');
            $table->index('description');
        });
    }

    /**
     * 回滚迁移
     */
    public function down(): void
    {
        Schema::dropIfExists('wechat_groups');
    }
};