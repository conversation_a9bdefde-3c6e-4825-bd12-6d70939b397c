<?php

require_once __DIR__ . '/vendor/autoload.php';

// 手动加载 .env 文件
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

echo "DB_CONNECTION from _ENV: " . ($_ENV['DB_CONNECTION'] ?? 'not set') . PHP_EOL;
echo "DB_CONNECTION from getenv: " . (getenv('DB_CONNECTION') ?: 'not set') . PHP_EOL;
echo "DB_DATABASE from _ENV: " . ($_ENV['DB_DATABASE'] ?? 'not set') . PHP_EOL;

// 检查文件是否存在
$envFile = __DIR__ . '/.env';
echo ".env file exists: " . (file_exists($envFile) ? 'yes' : 'no') . PHP_EOL;
echo ".env file readable: " . (is_readable($envFile) ? 'yes' : 'no') . PHP_EOL;

// 读取 .env 文件内容（前几行）
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES);
    echo "\nFirst 20 lines of .env:\n";
    foreach (array_slice($lines, 0, 20) as $i => $line) {
        echo ($i + 1) . ": " . $line . PHP_EOL;
    }
}