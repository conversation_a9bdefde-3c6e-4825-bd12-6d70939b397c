<?php

require_once __DIR__ . '/vendor/autoload.php';

// 启动Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "Laravel environment check:\n";
echo "_ENV['DB_CONNECTION']: " . ($_ENV['DB_CONNECTION'] ?? 'not set') . PHP_EOL;
echo "getenv('DB_CONNECTION'): " . (getenv('DB_CONNECTION') ?: 'not set') . PHP_EOL;
echo "env('DB_CONNECTION'): " . (env('DB_CONNECTION') ?: 'not set') . PHP_EOL;
echo "config('database.default'): " . config('database.default') . PHP_EOL;
echo "config('database.connections.sqlite.database'): " . config('database.connections.sqlite.database') . PHP_EOL;

// 尝试连接数据库
try {
    $pdo = new PDO('sqlite:' . config('database.connections.sqlite.database'));
    echo "SQLite direct connection: SUCCESS\n";
} catch (Exception $e) {
    echo "SQLite direct connection: FAILED - " . $e->getMessage() . "\n";
}

// 检查Laravel数据库连接
try {
    $connection = DB::connection();
    echo "Laravel DB connection type: " . $connection->getDriverName() . "\n";
    echo "Laravel DB connection: SUCCESS\n";
} catch (Exception $e) {
    echo "Laravel DB connection: FAILED - " . $e->getMessage() . "\n";
}