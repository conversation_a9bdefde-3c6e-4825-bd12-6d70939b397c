<template>
  <div class="qr-code-canvas-container">
    <div v-if="loading" class="loading-container">
      <el-icon class="is-loading">
        <Loading />
      </el-icon>
      <span>生成中...</span>
    </div>
    
    <canvas 
      v-show="!loading"
      ref="canvasRef" 
      :width="size" 
      :height="size"
      :class="canvasClass"
      @click="handleCanvasClick"
    ></canvas>
    
    <div v-if="error" class="error-container">
      <el-icon class="error-icon">
        <Warning />
      </el-icon>
      <span>{{ error }}</span>
      <el-button size="small" type="primary" @click="regenerate">重新生成</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, nextTick, onMounted } from 'vue'
import { ElMessage, ElIcon } from 'element-plus'
import { Loading, Warning } from '@element-plus/icons-vue'
import QRCode from 'qrcode'

// Props
const props = defineProps({
  text: {
    type: String,
    required: true
  },
  size: {
    type: Number,
    default: 200
  },
  margin: {
    type: Number,
    default: 2
  },
  errorCorrectionLevel: {
    type: String,
    default: 'M',
    validator: (value) => ['L', 'M', 'Q', 'H'].includes(value)
  },
  color: {
    type: Object,
    default: () => ({
      dark: '#000000',
      light: '#FFFFFF'
    })
  },
  canvasClass: {
    type: String,
    default: ''
  },
  autoGenerate: {
    type: Boolean,
    default: true
  },
  enableFallback: {
    type: Boolean,
    default: true
  }
})

// Emits
const emit = defineEmits(['generated', 'error', 'click'])

// Refs
const canvasRef = ref(null)
const loading = ref(false)
const error = ref('')

// Methods
const generateQRCode = async () => {
  if (!props.text) {
    error.value = '请提供要生成二维码的内容'
    return
  }

  loading.value = true
  error.value = ''

  try {
    await nextTick()
    
    if (!canvasRef.value) {
      throw new Error('Canvas元素未找到')
    }

    const canvas = canvasRef.value
    const ctx = canvas.getContext('2d')
    
    // 清空canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height)

    // 生成二维码配置
    const options = {
      width: props.size,
      height: props.size,
      margin: props.margin,
      color: props.color,
      errorCorrectionLevel: props.errorCorrectionLevel
    }

    // 生成二维码到canvas
    await QRCode.toCanvas(canvas, props.text, options)
    
    // 获取DataURL
    const dataUrl = canvas.toDataURL('image/png')
    
    emit('generated', {
      dataUrl,
      canvas,
      text: props.text
    })
    
    ElMessage.success('二维码生成成功')
    
  } catch (err) {
    console.error('QR Code generation failed:', err)
    error.value = err.message || '生成二维码失败'
    
    emit('error', {
      error: err,
      text: props.text
    })
    
    // 尝试备用方案
    if (props.enableFallback) {
      await tryFallbackGeneration()
    }
  } finally {
    loading.value = false
  }
}

// 备用生成方案
const tryFallbackGeneration = async () => {
  try {
    const fallbackUrl = `https://api.qrserver.com/v1/create-qr-code/?size=${props.size}x${props.size}&data=${encodeURIComponent(props.text)}`
    
    const img = new Image()
    img.crossOrigin = 'anonymous'
    
    img.onload = () => {
      const canvas = canvasRef.value
      const ctx = canvas.getContext('2d')
      ctx.clearRect(0, 0, canvas.width, canvas.height)
      ctx.drawImage(img, 0, 0, props.size, props.size)
      
      const dataUrl = canvas.toDataURL('image/png')
      emit('generated', {
        dataUrl,
        canvas,
        text: props.text,
        fallback: true
      })
      
      error.value = ''
      ElMessage.success('二维码生成成功（使用备用方案）')
    }
    
    img.onerror = () => {
      error.value = '二维码生成失败，请检查网络连接'
    }
    
    img.src = fallbackUrl
    
  } catch (fallbackError) {
    console.error('Fallback generation failed:', fallbackError)
    error.value = '二维码生成失败，请稍后重试'
  }
}

// 重新生成
const regenerate = () => {
  generateQRCode()
}

// Canvas点击事件
const handleCanvasClick = () => {
  emit('click', {
    canvas: canvasRef.value,
    text: props.text
  })
}

// 获取DataURL
const getDataURL = () => {
  if (canvasRef.value) {
    return canvasRef.value.toDataURL('image/png')
  }
  return null
}

// 下载二维码
const download = (filename = '二维码.png') => {
  const dataUrl = getDataURL()
  if (dataUrl) {
    const link = document.createElement('a')
    link.href = dataUrl
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    ElMessage.success('二维码下载成功')
  } else {
    ElMessage.error('没有可下载的二维码')
  }
}

// 暴露方法
defineExpose({
  generateQRCode,
  regenerate,
  getDataURL,
  download,
  canvas: canvasRef
})

// 监听text变化
watch(() => props.text, () => {
  if (props.autoGenerate && props.text) {
    generateQRCode()
  }
}, { immediate: true })

// 组件挂载后生成
onMounted(() => {
  if (props.autoGenerate && props.text) {
    generateQRCode()
  }
})
</script>

<style scoped>
.qr-code-canvas-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #666;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #f56c6c;
  text-align: center;
}

.error-icon {
  font-size: 24px;
}

canvas {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  cursor: pointer;
}

canvas:hover {
  border-color: #409eff;
}
</style>
