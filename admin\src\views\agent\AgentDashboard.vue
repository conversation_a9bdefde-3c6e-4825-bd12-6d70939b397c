<template>
  <div class="agent-dashboard" v-loading="isLoading" element-loading-text="加载中...">
    <!-- 性能监控组件 -->
    <PerformanceMonitor ref="performanceMonitor" />
    
    <!-- 页面头部 - 参考分销员设计 -->
    <div class="page-header">
      <div class="agent-info">
        <el-avatar :size="60" :src="userStore.avatar || defaultAvatar" />
        <div class="info-content">
          <h2>{{ userStore.nickname || '代理商' }}</h2>
          <p>代理商ID: {{ agentCode }}</p>
          <div class="status-tags">
            <el-tag type="success" effect="light">活跃代理</el-tag>
            <el-tag type="primary" effect="light">{{ levelText }}</el-tag>
          </div>
        </div>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="goToTeamManagement" :loading="buttonLoading.team">
          <el-icon><Connection /></el-icon>
          团队管理
        </el-button>
        <el-button @click="goToPromotionTools" :loading="buttonLoading.promotion">
          <el-icon><Share /></el-icon>
          推广工具
        </el-button>
      </div>
    </div>

    <!-- 核心数据统计 - 参考分销员设计 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6" v-for="(stat, index) in statsData" :key="index">
        <div class="stat-card" @click="handleStatClick(stat.key)">
          <div class="stat-icon" :style="{ backgroundColor: stat.color + '20', color: stat.color }">
            <el-icon :size="24">
              <component :is="stat.icon" />
            </el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">
              {{ stat.prefix }}{{ formatNumber(stat.value) }}{{ stat.suffix }}
            </div>
            <div class="stat-title">{{ stat.title }}</div>
            <div class="stat-trend" v-if="stat.trend" :class="getTrendClass(stat.trend)">
              <el-icon><ArrowUp v-if="stat.trend > 0" /><ArrowDown v-else /></el-icon>
              {{ Math.abs(stat.trend) }}%
            </div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 快捷操作面板 - 代理商专属功能 -->
    <el-card class="quick-actions-card">
      <template #header>
        <span>快捷操作</span>
      </template>
      <el-row :gutter="15">
        <el-col :span="4">
          <div class="action-item" @click="goToTeamManagement">
            <el-icon class="action-icon"><Connection /></el-icon>
            <span>团队管理</span>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="action-item" @click="recruitAgent">
            <el-icon class="action-icon"><UserFilled /></el-icon>
            <span>招募代理</span>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="action-item" @click="goToPromotionTools">
            <el-icon class="action-icon"><Share /></el-icon>
            <span>推广工具</span>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="action-item" @click="goToCommissionCenter">
            <el-icon class="action-icon"><Money /></el-icon>
            <span>佣金中心</span>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="action-item" @click="goToPerformanceAnalysis">
            <el-icon class="action-icon"><TrendCharts /></el-icon>
            <span>绩效分析</span>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="action-item" @click="goToTrainingCenter">
            <el-icon class="action-icon"><Reading /></el-icon>
            <span>培训中心</span>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 数据图表区域 - 参考分销员设计 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :span="16">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>佣金收入趋势</span>
              <el-radio-group v-model="revenuePeriod" size="small" @change="loadRevenueData">
                <el-radio-button value="7d">近7天</el-radio-button>
                <el-radio-button value="30d">近30天</el-radio-button>
                <el-radio-button value="90d">近3个月</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <LineChart
            :data="revenueChartData"
            :options="chartOptions"
            height="300px"
          />
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card>
          <template #header>
            <span>团队分布</span>
          </template>
          <DoughnutChart
            :data="teamDistributionData"
            :options="doughnutOptions"
            height="300px"
          />
        </el-card>
      </el-col>
    </el-row>

    <!-- 团队动态和招募管理 - 代理商专属 -->
    <el-row :gutter="20" class="info-row">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>团队动态</span>
              <el-button size="small" @click="loadTeamActivities">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </template>
          <div class="team-activities">
            <div v-for="activity in teamActivities" :key="activity.id" class="activity-item">
              <div class="activity-avatar">
                <el-avatar :size="32" :src="activity.member?.avatar" />
              </div>
              <div class="activity-content">
                <div class="activity-title">{{ activity.title }}</div>
                <div class="activity-desc">{{ activity.description }}</div>
                <div class="activity-time">{{ formatTime(activity.created_at) }}</div>
              </div>
              <div class="activity-value" v-if="activity.value">
                <span class="value-amount">¥{{ activity.value }}</span>
              </div>
            </div>
            <div v-if="teamActivities.length === 0" class="empty-state">
              <el-empty description="暂无团队动态" />
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>招募管理</span>
              <el-badge :value="pendingRecruits.length" class="recruit-badge">
                <el-button size="small" @click="goToRecruitList">查看全部</el-button>
              </el-badge>
            </div>
          </template>
          <div class="recruit-list">
            <div v-for="recruit in pendingRecruits" :key="recruit.id" class="recruit-item">
              <div class="recruit-info">
                <div class="recruit-name">{{ recruit.name }}</div>
                <div class="recruit-level">
                  <el-tag :type="getRecruitLevelColor(recruit.target_level)" size="small">
                    申请{{ recruit.target_level_text }}
                  </el-tag>
                </div>
              </div>
              <div class="recruit-time">
                <span class="time-label">申请时间:</span>
                <span class="time-value">
                  {{ formatFollowUpTime(recruit.created_at) }}
                </span>
              </div>
              <div class="recruit-actions">
                <el-button size="small" type="primary" @click="approveRecruit(recruit)">
                  批准
                </el-button>
              </div>
            </div>
            <div v-if="pendingRecruits.length === 0" class="empty-state">
              <el-empty description="暂无待处理申请" />
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 推广链接快速生成 -->
    <el-card class="promotion-card">
      <template #header>
        <span>推广链接快速生成</span>
      </template>
      <el-row :gutter="20">
        <el-col :span="16">
          <el-input
            v-model="promotionLink"
            placeholder="您的专属推广链接"
            readonly
          >
            <template #prepend>推广链接</template>
            <template #append>
              <el-button @click="copyPromotionLink">
                <el-icon><DocumentCopy /></el-icon>
                复制
              </el-button>
            </template>
          </el-input>
        </el-col>
        <el-col :span="8">
          <el-button type="primary" @click="generateQRCode">
            <el-icon><Setting /></el-icon>
            生成二维码
          </el-button>
          <el-button @click="goToPromotionTools">
            <el-icon><Setting /></el-icon>
            更多工具
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 二维码对话框 -->
    <el-dialog v-model="qrCodeDialogVisible" title="推广二维码" width="400px">
      <div class="qr-code-container">
        <canvas ref="qrCodeRef" class="qr-code" width="200" height="200"></canvas>
        <div class="qr-code-tips">
          <p>扫描二维码或分享链接进行推广</p>
          <el-button type="primary" @click="downloadQRCode">下载二维码</el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 招募代理对话框 -->
    <el-dialog
      v-model="showRecruitDialog"
      title="招募代理商"
      width="800px"
      :close-on-click-modal="false"
      class="recruit-dialog"
    >
      <el-form
        ref="recruitFormRef"
        :model="recruitForm"
        :rules="recruitRules"
        label-width="120px"
        class="recruit-form"
      >
        <div class="form-section">
          <h4 class="section-title">基本信息</h4>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="姓名" prop="name">
                <el-input
                  v-model="recruitForm.name"
                  placeholder="请输入被招募人姓名"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="手机号码" prop="phone">
                <el-input
                  v-model="recruitForm.phone"
                  placeholder="请输入手机号码"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="邮箱地址" prop="email">
                <el-input
                  v-model="recruitForm.email"
                  placeholder="请输入邮箱地址"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="微信号">
                <el-input
                  v-model="recruitForm.wechat"
                  placeholder="请输入微信号"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <div class="form-section">
          <h4 class="section-title">工作信息</h4>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="公司名称">
                <el-input
                  v-model="recruitForm.company"
                  placeholder="请输入公司名称"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="职位">
                <el-input
                  v-model="recruitForm.position"
                  placeholder="请输入职位"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="相关经验">
            <el-input
              v-model="recruitForm.experience"
              type="textarea"
              :rows="3"
              placeholder="请描述被招募人的相关工作经验或推广经验"
            />
          </el-form-item>
        </div>

        <div class="form-section">
          <h4 class="section-title">代理信息</h4>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="代理类型" prop="target_level">
                <el-select v-model="recruitForm.target_level" placeholder="请选择代理类型" style="width: 100%">
                  <el-option label="个人代理" value="individual" />
                  <el-option label="企业代理" value="enterprise" />
                  <el-option label="渠道代理" value="channel" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="期望佣金">
                <el-input
                  v-model="recruitForm.expected_commission"
                  placeholder="如：10%"
                  clearable
                >
                  <template #append>%</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <div class="form-section">
          <h4 class="section-title">推荐信息</h4>
          <el-form-item label="推荐理由" prop="reason">
            <el-input
              v-model="recruitForm.reason"
              type="textarea"
              :rows="4"
              placeholder="请详细说明推荐该人员成为代理商的理由，包括其优势、能力、资源等"
            />
          </el-form-item>

          <el-form-item label="备注信息">
            <el-input
              v-model="recruitForm.remark"
              type="textarea"
              :rows="2"
              placeholder="其他需要说明的信息（选填）"
            />
          </el-form-item>
        </div>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showRecruitDialog = false">取消</el-button>
          <el-button @click="resetRecruitForm">重置</el-button>
          <el-button @click="showRecruitRecords = true">查看记录</el-button>
          <el-button
            type="primary"
            @click="handleRecruitSubmit"
            :loading="recruitLoading"
          >
            发送邀请
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 招募记录对话框 -->
    <el-dialog
      v-model="showRecruitRecords"
      title="招募记录管理"
      width="1200px"
      class="recruit-records-dialog"
    >
      <div class="recruit-stats">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ recruitStats.total }}</div>
              <div class="stat-label">总招募数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value pending">{{ recruitStats.pending }}</div>
              <div class="stat-label">待处理</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value approved">{{ recruitStats.approved }}</div>
              <div class="stat-label">已通过</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value rejected">{{ recruitStats.rejected }}</div>
              <div class="stat-label">已拒绝</div>
            </div>
          </el-col>
        </el-row>
      </div>

      <el-table :data="recruitRecords" style="width: 100%" class="recruit-table">
        <el-table-column prop="name" label="姓名" width="100" />
        <el-table-column prop="phone" label="手机号" width="120" />
        <el-table-column prop="email" label="邮箱" width="180" />
        <el-table-column prop="target_level" label="代理类型" width="100">
          <template #default="{ row }">
            <el-tag size="small">{{ getLevelText(row.target_level) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="invite_code" label="邀请码" width="100">
          <template #default="{ row }">
            <div class="invite-code">
              <span>{{ row.invite_code }}</span>
              <el-button
                link
                type="primary"
                size="small"
                @click="copyInviteCode(row.invite_code)"
              >
                复制
              </el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="招募时间" width="120">
          <template #default="{ row }">
            {{ formatTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button
              v-if="row.status === 'pending'"
              link
              type="primary"
              size="small"
              @click="resendInvite(row)"
            >
              重发邀请
            </el-button>
            <el-button
              link
              type="info"
              size="small"
              @click="viewRecruitDetail(row)"
            >
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showRecruitRecords = false">关闭</el-button>
          <el-button type="primary" @click="loadRecruitRecords">刷新数据</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 功能说明对话框 -->
    <el-dialog
      v-model="showHelpDialog"
      title="代理商工作台功能说明"
      width="1000px"
      class="help-dialog"
    >
      <div class="help-content">
        <!-- 功能概述 -->
        <div class="help-section">
          <h3>🎯 功能概述</h3>
          <p>代理商工作台是您管理推广业务的核心平台，提供全面的数据统计、团队管理、佣金跟踪等功能，帮助您高效开展推广业务，最大化收益。</p>
        </div>

        <!-- 核心功能 -->
        <div class="help-section">
          <h3>🚀 核心功能模块</h3>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="feature-item">
                <div class="feature-icon">
                  <el-icon><Share /></el-icon>
                </div>
                <div class="feature-content">
                  <h4>推广工具</h4>
                  <p>专属推广链接、二维码生成、推广素材管理</p>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="feature-item">
                <div class="feature-icon">
                  <el-icon><Connection /></el-icon>
                </div>
                <div class="feature-content">
                  <h4>团队管理</h4>
                  <p>下级代理商管理、团队业绩统计、层级关系维护</p>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="feature-item">
                <div class="feature-icon">
                  <el-icon><Money /></el-icon>
                </div>
                <div class="feature-content">
                  <h4>佣金中心</h4>
                  <p>佣金收入统计、提现申请、收益明细查询</p>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="feature-item">
                <div class="feature-icon">
                  <el-icon><TrendCharts /></el-icon>
                </div>
                <div class="feature-content">
                  <h4>绩效分析</h4>
                  <p>推广数据分析、转化率统计、业绩趋势图表</p>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="feature-item">
                <div class="feature-icon">
                  <el-icon><Reading /></el-icon>
                </div>
                <div class="feature-content">
                  <h4>培训中心</h4>
                  <p>推广技巧学习、产品知识培训、营销资料下载</p>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="feature-item">
                <div class="feature-icon">
                  <el-icon><Setting /></el-icon>
                </div>
                <div class="feature-content">
                  <h4>账户设置</h4>
                  <p>个人信息管理、收款账户设置、通知偏好配置</p>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 代理商等级说明 -->
        <div class="help-section">
          <h3>🏆 代理商等级体系</h3>
          <el-table :data="agentLevels" style="width: 100%">
            <el-table-column prop="level" label="等级" width="100">
              <template #default="{ row }">
                <el-tag :type="row.color">{{ row.level }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="等级名称" width="120" />
            <el-table-column prop="requirements" label="升级条件" />
            <el-table-column prop="commission_rate" label="佣金比例" width="100" />
            <el-table-column prop="benefits" label="专属权益" />
          </el-table>
        </div>

        <!-- 佣金计算规则 -->
        <div class="help-section">
          <h3>💰 佣金计算规则</h3>
          <div class="commission-rules">
            <div class="rule-item">
              <h4>🔸 直推佣金</h4>
              <p>直接推广用户产生的订单，您可获得 <strong>{{ commissionRates.direct }}%</strong> 的佣金</p>
              <div class="example">
                <span class="example-label">示例：</span>
                用户通过您的链接购买100元产品，您获得{{ commissionRates.direct }}元佣金
              </div>
            </div>
            <div class="rule-item">
              <h4>🔸 团队佣金</h4>
              <p>下级代理商推广产生的订单，您可获得 <strong>{{ commissionRates.team }}%</strong> 的团队佣金</p>
              <div class="example">
                <span class="example-label">示例：</span>
                下级代理推广100元订单，您获得{{ commissionRates.team }}元团队佣金
              </div>
            </div>
            <div class="rule-item">
              <h4>🔸 层级佣金</h4>
              <p>支持多层级佣金分配，最多支持 <strong>{{ commissionRules.max_levels }}</strong> 级分佣</p>
              <el-table :data="levelCommissions" size="small" style="margin-top: 10px;">
                <el-table-column prop="level" label="层级" width="80" />
                <el-table-column prop="rate" label="佣金比例" width="100" />
                <el-table-column prop="description" label="说明" />
              </el-table>
            </div>
          </div>
        </div>

        <!-- 推广技巧 -->
        <div class="help-section">
          <h3>📈 推广技巧与建议</h3>
          <el-tabs v-model="activeTab" type="card">
            <el-tab-pane label="推广渠道" name="channels">
              <div class="tips-content">
                <h4>🌟 推荐推广渠道</h4>
                <ul>
                  <li><strong>社交媒体</strong>：微信朋友圈、QQ空间、微博等社交平台分享</li>
                  <li><strong>社群营销</strong>：微信群、QQ群、论坛等社群推广</li>
                  <li><strong>内容营销</strong>：撰写产品评测、使用心得等优质内容</li>
                  <li><strong>线下推广</strong>：朋友推荐、活动宣传等线下渠道</li>
                  <li><strong>短视频平台</strong>：抖音、快手等短视频平台推广</li>
                </ul>
                <el-alert type="success" :closable="false" style="margin-top: 15px;">
                  💡 建议：多渠道组合推广，提高覆盖面和转化率
                </el-alert>
              </div>
            </el-tab-pane>
            <el-tab-pane label="推广话术" name="scripts">
              <div class="tips-content">
                <h4>💬 推广话术模板</h4>
                <div class="script-item">
                  <h5>朋友圈推广</h5>
                  <div class="script-text">
                    "发现一个不错的平台，可以通过推广赚取佣金💰<br>
                    产品质量有保障，佣金结算及时✅<br>
                    感兴趣的朋友可以了解一下👇<br>
                    [推广链接]"
                  </div>
                </div>
                <div class="script-item">
                  <h5>私聊推广</h5>
                  <div class="script-text">
                    "Hi，最近在做一个项目，产品不错，佣金也挺可观的。<br>
                    如果你有兴趣了解或者想要产品的话，可以通过我的链接购买，<br>
                    这样我也能获得一些佣金收入😊<br>
                    链接：[推广链接]"
                  </div>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="注意事项" name="notes">
              <div class="tips-content">
                <h4>⚠️ 推广注意事项</h4>
                <ul>
                  <li><strong>诚信推广</strong>：如实介绍产品特点，不夸大宣传</li>
                  <li><strong>合规操作</strong>：遵守平台规则，不进行违规推广</li>
                  <li><strong>用户体验</strong>：关注用户反馈，提供优质服务</li>
                  <li><strong>持续学习</strong>：关注产品更新，学习推广技巧</li>
                  <li><strong>数据分析</strong>：定期分析推广数据，优化推广策略</li>
                </ul>
                <el-alert type="warning" :closable="false" style="margin-top: 15px;">
                  ⚠️ 警告：严禁虚假宣传、恶意刷单等违规行为，一经发现将取消代理资格
                </el-alert>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>

        <!-- 操作指南 -->
        <div class="help-section">
          <h3>📝 操作指南</h3>
          <el-collapse v-model="activeGuides">
            <el-collapse-item title="如何生成推广链接？" name="link-generation">
              <div class="guide-content">
                <ol>
                  <li>在工作台下方找到"推广链接快速生成"区域</li>
                  <li>系统会自动生成您的专属推广链接</li>
                  <li>点击"复制"按钮复制链接到剪贴板</li>
                  <li>也可以点击"生成二维码"创建推广二维码</li>
                  <li>将链接或二维码分享给潜在用户</li>
                </ol>
                <el-alert type="info" :closable="false">
                  💡 提示：推广链接包含您的专属代理编码，用户通过此链接注册购买，您将获得相应佣金
                </el-alert>
              </div>
            </el-collapse-item>
            
            <el-collapse-item title="如何查看佣金收入？" name="commission-check">
              <div class="guide-content">
                <ol>
                  <li>在统计卡片中查看总佣金收入和本月佣金</li>
                  <li>点击"佣金中心"查看详细的佣金明细</li>
                  <li>在佣金收入趋势图中查看收入变化</li>
                  <li>可以按时间段筛选查看不同期间的收入</li>
                </ol>
                <el-alert type="success" :closable="false">
                  ✅ 说明：佣金每日结算，T+1到账，可在佣金中心申请提现
                </el-alert>
              </div>
            </el-collapse-item>
            
            <el-collapse-item title="如何管理我的团队？" name="team-management">
              <div class="guide-content">
                <ol>
                  <li>在"我的团队"卡片中查看团队概况</li>
                  <li>点击"团队管理"进入详细的团队管理页面</li>
                  <li>可以查看下级代理商的业绩和状态</li>
                  <li>为团队成员提供培训和指导</li>
                  <li>关注团队成员的推广数据和收益情况</li>
                </ol>
                <el-alert type="info" :closable="false">
                  💡 建议：定期与团队成员沟通，分享推广经验，共同提升业绩
                </el-alert>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>

        <!-- 常见问题 -->
        <div class="help-section">
          <h3>❓ 常见问题</h3>
          <el-collapse v-model="activeFAQ">
            <el-collapse-item title="佣金什么时候到账？" name="faq1">
              <p>佣金采用T+1结算模式，即今日产生的佣金将在明日到账。您可以在佣金中心查看详细的结算记录。</p>
            </el-collapse-item>
            <el-collapse-item title="如何提升代理商等级？" name="faq2">
              <p>代理商等级根据您的推广业绩自动评定，包括推广用户数、团队规模、佣金收入等指标。持续推广并发展团队即可提升等级。</p>
            </el-collapse-item>
            <el-collapse-item title="推广链接有有效期吗？" name="faq3">
              <p>推广链接长期有效，但建议定期更新推广素材。如果您的代理资格发生变化，系统会自动更新链接状态。</p>
            </el-collapse-item>
            <el-collapse-item title="可以同时推广多个产品吗？" name="faq4">
              <p>可以的。您可以推广平台上的所有产品，每个产品的佣金比例可能不同，具体以产品页面显示为准。</p>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, nextTick } from 'vue'

// 模板引用
const qrCodeRef = ref(null)
const recruitFormRef = ref(null)
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage, ElNotification } from 'element-plus'
import {
  User, UserFilled, Connection, Share, Money, Reading, TrendCharts, Setting,
  Refresh, ArrowUp, ArrowDown, QuestionFilled, DocumentCopy, Coin
} from '@element-plus/icons-vue'
import { ElMessageBox } from 'element-plus'
import LineChart from '@/components/Charts/LineChart.vue'
import DoughnutChart from '@/components/Charts/DoughnutChart.vue'
import PerformanceMonitor from '@/components/PerformanceMonitor.vue'

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const isLoading = ref(false)
const revenuePeriod = ref('30d')
const defaultAvatar = '/avatars/default.jpg'

// 加载状态管理
const buttonLoading = reactive({
  team: false,
  promotion: false
})

// 数据状态
const teamActivities = ref([])
const pendingRecruits = ref([])
const promotionLink = ref('')

// 招募代理对话框相关
const showRecruitDialog = ref(false)
const recruitLoading = ref(false)
const recruitForm = reactive({
  name: '',
  phone: '',
  email: '',
  wechat: '',
  company: '',
  position: '',
  experience: '',
  target_level: 'individual',
  expected_commission: '',
  reason: '',
  remark: ''
})

const recruitRules = {
  name: [
    { required: true, message: '请输入被招募人姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '姓名长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号码', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  target_level: [
    { required: true, message: '请选择代理商类型', trigger: 'change' }
  ],
  reason: [
    { required: true, message: '请填写推荐理由', trigger: 'blur' },
    { min: 10, max: 500, message: '推荐理由长度在 10 到 500 个字符', trigger: 'blur' }
  ]
}

// 招募记录相关
const showRecruitRecords = ref(false)
const recruitRecords = ref([])
const recruitStats = ref({
  total: 0,
  pending: 0,
  approved: 0,
  rejected: 0
})

// 统计数据 - 参考分销员工作台结构
const statsData = ref([
  {
    key: 'team_members',
    title: '团队成员',
    value: 0,
    icon: 'Connection',
    color: '#409EFF',
    trend: 0,
    prefix: '',
    suffix: '人'
  },
  {
    key: 'total_commission',
    title: '累计佣金',
    value: 0,
    icon: 'Money',
    color: '#67C23A',
    trend: 0,
    prefix: '¥',
    suffix: ''
  },
  {
    key: 'month_commission',
    title: '本月佣金',
    value: 0,
    icon: 'Coin',
    color: '#E6A23C',
    trend: 0,
    prefix: '¥',
    suffix: ''
  },
  {
    key: 'recruit_rate',
    title: '招募转化率',
    value: 0,
    icon: 'TrendCharts',
    color: '#F56C6C',
    trend: 0,
    prefix: '',
    suffix: '%'
  }
])

// 图表数据 - 参考分销员工作台
const revenueChartData = ref({
  labels: [],
  datasets: [{
    label: '佣金收入',
    data: [],
    borderColor: '#409EFF',
    backgroundColor: 'rgba(64, 158, 255, 0.1)',
    tension: 0.4,
    fill: true
  }]
})

const teamDistributionData = ref({
  labels: ['直属代理', '二级代理', '三级代理', '其他'],
  datasets: [{
    data: [15, 25, 35, 8],
    backgroundColor: ['#F56C6C', '#E6A23C', '#409EFF', '#67C23A'],
    borderWidth: 0,
    hoverOffset: 4
  }]
})

const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  interaction: {
    intersect: false,
    mode: 'index'
  },
  plugins: {
    legend: {
      display: true,
      position: 'top'
    }
  },
  scales: {
    y: {
      beginAtZero: true,
      grid: {
        color: 'rgba(0, 0, 0, 0.1)'
      }
    },
    x: {
      grid: {
        display: false
      }
    }
  }
}

const doughnutOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'bottom',
      labels: {
        padding: 20,
        usePointStyle: true
      }
    }
  }
}

// 计算属性
const agentCode = computed(() => {
  return userStore.userInfo?.agent_code || 'A' + (userStore.userInfo?.id || '001')
})

const levelText = computed(() => {
  const level = userStore.userInfo?.agent_level || '初级代理'
  return level
})

// 原有数据结构保持兼容
const agentStatCards = ref([
  {
    key: 'total_users',
    label: '推广用户数',
    value: '0',
    icon: 'User',
    color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '+15.2%'
  },
  {
    key: 'total_commission',
    label: '总佣金收入',
    value: '¥0',
    icon: 'Money',
    color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '+28.5%'
  },
  {
    key: 'monthly_commission',
    label: '本月佣金',
    value: '¥0',
    icon: 'Coin',
    color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '+18.2%'
  },
  {
    key: 'child_agents',
    label: '下级代理商',
    value: '0',
    icon: 'Connection',
    color: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '+5'
  }
])

// 帮助对话框相关数据
const activeTab = ref('channels')
const activeGuides = ref(['link-generation'])
const activeFAQ = ref([])

// 代理商等级数据
const agentLevels = ref([
  {
    level: '初级代理',
    color: 'info',
    name: '新手代理',
    requirements: '注册成功，完成实名认证',
    commission_rate: '5%',
    benefits: '基础推广工具、新手培训'
  },
  {
    level: '中级代理',
    color: 'primary',
    name: '进阶代理',
    requirements: '推广用户≥10人，月佣金≥500元',
    commission_rate: '8%',
    benefits: '专属客服、营销素材、数据分析'
  },
  {
    level: '高级代理',
    color: 'warning',
    name: '资深代理',
    requirements: '推广用户≥50人，团队≥5人，月佣金≥2000元',
    commission_rate: '12%',
    benefits: '优先结算、专属培训、活动优先权'
  },
  {
    level: '金牌代理',
    color: 'danger',
    name: '顶级代理',
    requirements: '推广用户≥200人，团队≥20人，月佣金≥10000元',
    commission_rate: '15%',
    benefits: '专属经理、定制服务、年度奖励'
  }
])

// 佣金比例数据
const commissionRates = ref({
  direct: 10,
  team: 3
})

// 佣金规则数据
const commissionRules = ref({
  max_levels: 5
})

// 层级佣金数据
const levelCommissions = ref([
  { level: '1级', rate: '10%', description: '直接推广用户' },
  { level: '2级', rate: '3%', description: '下级代理推广用户' },
  { level: '3级', rate: '1%', description: '三级代理推广用户' },
  { level: '4级', rate: '0.5%', description: '四级代理推广用户' },
  { level: '5级', rate: '0.2%', description: '五级代理推广用户' }
])

// 图表数据
const commissionTrendData = ref({
  labels: [],
  datasets: [{
    label: '佣金收入',
    data: [],
    borderColor: '#409EFF',
    backgroundColor: 'rgba(64, 158, 255, 0.1)',
    tension: 0.4
  }]
})

const userSourceData = ref({
  labels: ['直接推广', '团队推广', '活动推广', '其他'],
  datasets: [{
    data: [0, 0, 0, 0],
    backgroundColor: ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C']
  }]
})

// 方法
const loadAgentInfo = async () => {
  try {
    const response = await agentApi.getMy()
    agentInfo.value = response.data
    generatePromotionLink()
  } catch (error) {
    ElMessage.error('加载代理商信息失败')
  }
}


const loadCommissionTrend = async () => {
  try {
    // 模拟数据，实际应该调用API
    const mockData = {
      week: {
        labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
        data: [120, 190, 300, 500, 200, 300, 450]
      },
      month: {
        labels: Array.from({length: 30}, (_, i) => `${i+1}日`),
        data: Array.from({length: 30}, () => Math.floor(Math.random() * 1000))
      },
      quarter: {
        labels: ['1月', '2月', '3月'],
        data: [8000, 12000, 15000]
      }
    }
    
    const data = mockData[commissionPeriod.value]
    commissionTrendData.value = {
      labels: data.labels,
      datasets: [{
        label: '佣金收入',
        data: data.data,
        borderColor: '#409EFF',
        backgroundColor: 'rgba(64, 158, 255, 0.1)',
        tension: 0.4
      }]
    }
  } catch (error) {
    ElMessage.error('加载佣金趋势失败')
  }
}

const loadTeamData = async () => {
  try {
    // 模拟团队数据
    teamStats.value = {
      direct_children: 12,
      total_children: 45,
      active_members: 38
    }
    
    recentMembers.value = [
      {
        id: 1,
        name: '张三',
        avatar: '',
        agent_type: 'individual',
        agent_type_text: '个人代理',
        created_at: new Date()
      },
      {
        id: 2,
        name: '李四',
        avatar: '',
        agent_type: 'enterprise',
        agent_type_text: '企业代理',
        created_at: new Date(Date.now() - 86400000)
      }
    ]
  } catch (error) {
    ElMessage.error('加载团队数据失败')
  }
}

const loadRecentActivities = async () => {
  try {
    // 模拟活动数据
    recentActivities.value = [
      {
        id: 1,
        type: 'commission',
        title: '佣金到账',
        description: '您获得了 ¥150.00 的推广佣金',
        created_at: new Date()
      },
      {
        id: 2,
        type: 'user',
        title: '新用户注册',
        description: '通过您的推广链接，新增用户"王五"',
        created_at: new Date(Date.now() - 3600000)
      },
      {
        id: 3,
        type: 'team',
        title: '团队成员升级',
        description: '下级代理商"赵六"升级为二级代理',
        created_at: new Date(Date.now() - 7200000)
      }
    ]
  } catch (error) {
    ElMessage.error('加载活动数据失败')
  }
}

const generatePromotionLink = () => {
  if (agentInfo.value.agent_code) {
    promotionLink.value = `${window.location.origin}/register?agent=${agentInfo.value.agent_code}`
  }
}

const copyPromotionLink = async () => {
  try {
    await navigator.clipboard.writeText(promotionLink.value)
    ElMessage.success('推广链接已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败，请手动复制')
  }
}

const generateQRCode = async () => {
  try {
    qrCodeDialogVisible.value = true
    await nextTick()

    // 确保canvas元素存在
    if (qrCodeRef.value) {
      const canvas = qrCodeRef.value
      const ctx = canvas.getContext('2d')
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      await QRCode.toCanvas(canvas, promotionLink.value, {
        width: 200,
        height: 200,
        margin: 2,
        errorCorrectionLevel: 'M'
      })

      ElMessage.success('二维码生成成功')
    }
  } catch (error) {
    console.error('生成二维码失败:', error)
    ElMessage.error('生成二维码失败')
  }
}

const downloadQRCode = () => {
  const canvas = qrCodeRef.value.querySelector('canvas')
  if (canvas) {
    const link = document.createElement('a')
    link.download = `推广二维码-${agentInfo.value.agent_code}.png`
    link.href = canvas.toDataURL()
    link.click()
  }
}

const refreshActivities = () => {
  loadRecentActivities()
  ElMessage.success('动态已刷新')
}

// 导航方法
const goToPromotionTools = () => {
  // 跳转到推广工具页面
  router.push('/admin/promotion/links')
}

const goToTeamManagement = () => {
  // 跳转到团队管理页面
  router.push('/admin/partners/hierarchy')
}

const goToCommissionCenter = () => {
  // 跳转到佣金中心
  router.push('/admin/partners/commission')
}

const goToTrainingCenter = () => {
  // 跳转到伙伴列表（代理商类型）
  router.push('/admin/partners/list?type=agent')
}

const goToPerformanceAnalysis = () => {
  console.log('🔧 绩效分析功能已修复 - 正在跳转！')
  ElMessage.success('绩效分析功能已修复！正在跳转到绩效分析页面...')
  router.push('/admin/agents/performance')
}

const goToSettings = () => {
  // 跳转到设置页面
  router.push('/admin/system/settings')
}

// 工具方法
const formatDate = (date) => {
  return new Date(date).toLocaleString('zh-CN')
}

const getMemberTypeColor = (type) => {
  const colors = {
    'individual': 'primary',
    'enterprise': 'success',
    'channel': 'warning'
  }
  return colors[type] || 'info'
}

const getActivityColor = (type) => {
  const colors = {
    'commission': '#67C23A',
    'user': '#409EFF',
    'team': '#E6A23C'
  }
  return colors[type] || '#909399'
}

const getActivityIcon = (type) => {
  const icons = {
    'commission': 'Money',
    'user': 'User',
    'team': 'Connection'
  }
  return icons[type] || 'InfoFilled'
}

// 刷新所有数据
const refreshAllData = async () => {
  try {
    await Promise.all([
      loadAgentInfo(),
      loadStats(),
      loadCommissionTrend(),
      loadTeamData(),
      loadRecentActivities()
    ])
    ElMessage.success('数据刷新成功')
  } catch (error) {
    ElMessage.error('数据刷新失败')
  }
}

// 模拟加载统计数据
const loadMockStats = async () => {
  try {
    statsLoading.value = true
    
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 模拟统计数据
    const mockStats = {
      total_users: 234,
      total_commission: 45670.89,
      monthly_commission: 8950.50,
      child_agents_count: 12
    }
    
    stats.value = mockStats
    
    // 更新统计卡片数据
    agentStatCards.value[0].value = mockStats.total_users.toString()
    agentStatCards.value[1].value = '¥' + mockStats.total_commission.toLocaleString()
    agentStatCards.value[2].value = '¥' + mockStats.monthly_commission.toLocaleString()
    agentStatCards.value[3].value = mockStats.child_agents_count.toString()
    
  } catch (error) {
    ElMessage.error('加载统计数据失败')
  } finally {
    statsLoading.value = false
  }
}

// 新增代理商专属方法 - 参考分销员工作台设计
const formatNumber = (num) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k'
  }
  return num.toString()
}

const getTrendClass = (trend) => {
  return trend > 0 ? 'trend-up' : 'trend-down'
}

const handleStatClick = (key) => {
  console.log('点击统计卡片:', key)
  switch (key) {
    case 'team_members':
      goToTeamManagement()
      break
    case 'total_commission':
    case 'month_commission':
      goToCommissionCenter()
      break
    case 'recruit_rate':
      goToPerformanceAnalysis()
      break
  }
}

const loadStats = async () => {
  try {
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 300))
    
    // 更新统计数据
    statsData.value[0].value = 85
    statsData.value[0].trend = 12.5
    statsData.value[1].value = 15600
    statsData.value[1].trend = 25.3
    statsData.value[2].value = 3200
    statsData.value[2].trend = 18.7
    statsData.value[3].value = 68.5
    statsData.value[3].trend = -3.2
    
    console.log('统计数据加载完成')
  } catch (error) {
    console.error('加载统计数据失败:', error)
    ElMessage.error('加载统计数据失败')
  }
}

const loadRevenueData = async () => {
  try {
    await new Promise(resolve => setTimeout(resolve, 500))
    
    const mockData = {
      '7d': {
        labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
        data: [250, 380, 450, 680, 420, 580, 750]
      },
      '30d': {
        labels: Array.from({length: 30}, (_, i) => `${i + 1}日`),
        data: Array.from({length: 30}, () => Math.floor(Math.random() * 1500) + 300)
      },
      '90d': {
        labels: ['第1月', '第2月', '第3月'],
        data: [12000, 18000, 22000]
      }
    }
    
    const data = mockData[revenuePeriod.value]
    revenueChartData.value = {
      labels: data.labels,
      datasets: [{
        label: '佣金收入',
        data: data.data,
        borderColor: '#409EFF',
        backgroundColor: 'rgba(64, 158, 255, 0.1)',
        tension: 0.4,
        fill: true
      }]
    }
  } catch (error) {
    ElMessage.error('加载收入数据失败')
  }
}

const loadTeamActivities = async () => {
  try {
    await new Promise(resolve => setTimeout(resolve, 400))
    
    teamActivities.value = [
      {
        id: 1,
        title: '新代理加入',
        description: '代理商"张经理"通过审核加入团队',
        created_at: new Date(),
        member: { avatar: defaultAvatar }
      },
      {
        id: 2,
        title: '佣金结算',
        description: '下级代理"李总"团队佣金结算',
        created_at: new Date(Date.now() - 3600000),
        value: 1580,
        member: { avatar: defaultAvatar }
      },
      {
        id: 3,
        title: '业绩达标',
        description: '代理商"王总"本月业绩突破10万',
        created_at: new Date(Date.now() - 7200000),
        member: { avatar: defaultAvatar }
      }
    ]
  } catch (error) {
    ElMessage.error('加载团队动态失败')
  }
}

const loadPendingRecruits = async () => {
  try {
    await new Promise(resolve => setTimeout(resolve, 350))
    
    pendingRecruits.value = [
      {
        id: 1,
        name: '陈老板',
        target_level: 'B',
        target_level_text: 'B级代理',
        created_at: new Date(Date.now() - 86400000)
      },
      {
        id: 2,
        name: '刘经理',
        target_level: 'A',
        target_level_text: 'A级代理',
        created_at: new Date(Date.now() - 172800000)
      }
    ]
  } catch (error) {
    ElMessage.error('加载招募申请失败')
  }
}

// 导航方法
const goToTeamManagement = async () => {
  buttonLoading.team = true
  try {
    await router.push('/admin/agents/hierarchy')
    ElNotification.success({
      title: '跳转成功',
      message: '已跳转到团队管理页面'
    })
  } catch (error) {
    ElMessage.warning('页面跳转失败，请稍后重试')
  } finally {
    buttonLoading.team = false
  }
}

const goToPromotionTools = async () => {
  buttonLoading.promotion = true
  try {
    await router.push('/admin/promotion/links')
    ElNotification.success({
      title: '跳转成功',
      message: '已跳转到推广工具页面'
    })
  } catch (error) {
    ElMessage.warning('页面跳转失败，请稍后重试')
  } finally {
    buttonLoading.promotion = false
  }
}

const recruitAgent = () => {
  console.log('🔧 招募代理功能已修复 - 点击生效！')
  ElMessage.success('招募代理功能已修复！正在打开招募对话框...')
  showRecruitDialog.value = true
}

// 招募代理相关方法
const resetRecruitForm = () => {
  Object.keys(recruitForm).forEach(key => {
    if (typeof recruitForm[key] === 'string') {
      recruitForm[key] = ''
    }
  })
  recruitForm.target_level = 'individual'
}

const handleRecruitSubmit = async () => {
  try {
    await recruitFormRef.value.validate()
    recruitLoading.value = true

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1500))

    // 创建招募记录
    const newRecord = {
      id: Date.now(),
      ...recruitForm,
      status: 'pending',
      created_at: new Date(),
      invite_code: generateInviteCode()
    }

    recruitRecords.value.unshift(newRecord)
    recruitStats.value.total++
    recruitStats.value.pending++

    ElMessage.success('招募邀请已发送成功！')
    showRecruitDialog.value = false
    resetRecruitForm()

    // 显示邀请码
    ElMessageBox.alert(
      `招募邀请已发送！邀请码：${newRecord.invite_code}`,
      '招募成功',
      {
        confirmButtonText: '确定',
        type: 'success'
      }
    )

  } catch (error) {
    if (error !== false) {
      ElMessage.error('发送招募邀请失败，请重试')
    }
  } finally {
    recruitLoading.value = false
  }
}

const generateInviteCode = () => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = 'INV'
  for (let i = 0; i < 6; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

const loadRecruitRecords = async () => {
  try {
    // 模拟加载招募记录
    await new Promise(resolve => setTimeout(resolve, 300))

    recruitRecords.value = [
      {
        id: 1,
        name: '张经理',
        phone: '13800138001',
        email: '<EMAIL>',
        target_level: 'enterprise',
        status: 'approved',
        created_at: new Date(Date.now() - 86400000 * 2),
        invite_code: 'INVAB123C'
      },
      {
        id: 2,
        name: '李总',
        phone: '13800138002',
        email: '<EMAIL>',
        target_level: 'individual',
        status: 'pending',
        created_at: new Date(Date.now() - 86400000),
        invite_code: 'INVDE456F'
      }
    ]

    recruitStats.value = {
      total: recruitRecords.value.length,
      pending: recruitRecords.value.filter(r => r.status === 'pending').length,
      approved: recruitRecords.value.filter(r => r.status === 'approved').length,
      rejected: recruitRecords.value.filter(r => r.status === 'rejected').length
    }

  } catch (error) {
    ElMessage.error('加载招募记录失败')
  }
}

const getStatusText = (status) => {
  const statusMap = {
    'pending': '待处理',
    'approved': '已通过',
    'rejected': '已拒绝'
  }
  return statusMap[status] || '未知'
}

const getStatusType = (status) => {
  const typeMap = {
    'pending': 'warning',
    'approved': 'success',
    'rejected': 'danger'
  }
  return typeMap[status] || 'info'
}

const getLevelText = (level) => {
  const levelMap = {
    'individual': '个人代理',
    'enterprise': '企业代理',
    'channel': '渠道代理'
  }
  return levelMap[level] || '未知'
}

const copyInviteCode = async (code) => {
  try {
    await navigator.clipboard.writeText(code)
    ElMessage.success('邀请码已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败，请手动复制')
  }
}

const resendInvite = async (record) => {
  try {
    await ElMessageBox.confirm(`确定要重新发送邀请给"${record.name}"吗？`, '确认重发', {
      type: 'warning'
    })

    // 模拟重发邀请
    await new Promise(resolve => setTimeout(resolve, 1000))

    ElMessage.success('邀请已重新发送')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('重发邀请失败')
    }
  }
}

const viewRecruitDetail = (record) => {
  ElMessageBox.alert(
    `
    <div style="text-align: left;">
      <p><strong>姓名：</strong>${record.name}</p>
      <p><strong>手机：</strong>${record.phone}</p>
      <p><strong>邮箱：</strong>${record.email || '未填写'}</p>
      <p><strong>微信：</strong>${record.wechat || '未填写'}</p>
      <p><strong>公司：</strong>${record.company || '未填写'}</p>
      <p><strong>职位：</strong>${record.position || '未填写'}</p>
      <p><strong>代理类型：</strong>${getLevelText(record.target_level)}</p>
      <p><strong>期望佣金：</strong>${record.expected_commission || '未填写'}</p>
      <p><strong>推荐理由：</strong>${record.reason || '未填写'}</p>
      <p><strong>备注：</strong>${record.remark || '无'}</p>
      <p><strong>邀请码：</strong>${record.invite_code}</p>
      <p><strong>状态：</strong>${getStatusText(record.status)}</p>
      <p><strong>招募时间：</strong>${formatTime(record.created_at)}</p>
    </div>
    `,
    '招募详情',
    {
      dangerouslyUseHTMLString: true,
      confirmButtonText: '确定'
    }
  )
}

const formatTime = (time) => {
  if (!time) return ''
  const date = new Date(time)
  return date.toLocaleDateString() + ' ' + date.toLocaleTimeString()
}

const goToCommissionCenter = () => {
  router.push('/admin/agents/commission')
}

const goToTrainingCenter = () => {
  ElMessage.info('培训中心功能开发中')
}

const approveRecruit = async (recruit) => {
  try {
    await ElMessageBox.confirm(`确定要批准"${recruit.name}"的${recruit.target_level_text}申请吗？`, '确认批准', {
      type: 'warning'
    })
    ElMessage.success('申请已批准')
    loadPendingRecruits()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

const goToRecruitList = () => {
  router.push('/admin/agents/applications')
}

// 工具方法
const getRecruitLevelColor = (level) => {
  const colors = {
    'A': 'danger',
    'B': 'warning',
    'C': 'primary'
  }
  return colors[level] || 'info'
}

const formatTime = (time) => {
  return new Date(time).toLocaleString('zh-CN', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatFollowUpTime = (time) => {
  const now = new Date()
  const targetTime = new Date(time)
  const diff = now - targetTime
  
  if (diff < 86400000) {
    const hours = Math.floor(diff / (1000 * 60 * 60))
    return `${hours}小时前`
  } else {
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    return `${days}天前`
  }
}

// 生命周期 - 优化加载流程
onMounted(async () => {
  console.log('代理商工作台开始加载...')
  isLoading.value = true
  
  try {
    // 并行加载所有数据
    await Promise.all([
      loadStats(),
      loadRevenueData(),
      loadTeamActivities(),
      loadPendingRecruits(),
      loadRecruitRecords()
    ])
    
    // 确保DOM更新完成
    await nextTick()
    
    console.log('代理商工作台加载完成')
    ElNotification.success({
      title: '加载完成',
      message: '工作台数据已全部加载完成',
      duration: 2000
    })
  } catch (error) {
    console.error('页面数据加载失败:', error)
    ElMessage.error('页面数据加载失败，请刷新重试')
  } finally {
    isLoading.value = false
  }
})
</script>

<style scoped>
.agent-dashboard {
  padding: 20px;
  background: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
  color: white;
}

.agent-info {
  display: flex;
  align-items: center;
}

.info-content {
  margin-left: 20px;
}

.info-content h2 {
  margin: 0 0 8px 0;
  color: white;
  font-size: 24px;
  font-weight: 600;
}

.info-content p {
  margin: 0 0 12px 0;
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
}

.status-tags {
  display: flex;
  gap: 12px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.header-actions .el-button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  backdrop-filter: blur(10px);
}

.header-actions .el-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.stats-row {
  margin-bottom: 24px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 24px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border-color: #409EFF;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  margin-bottom: 4px;
}

.stat-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 8px;
}

.stat-trend {
  display: flex;
  align-items: center;
  font-size: 12px;
  font-weight: 500;
}

.trend-up {
  color: #67C23A;
}

.trend-down {
  color: #F56C6C;
}

.quick-actions-card {
  margin-bottom: 24px;
  border-radius: 16px;
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24px 16px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid #f0f2f5;
  background: white;
}

.action-item:hover {
  background: #f8f9ff;
  border-color: #409EFF;
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(64, 158, 255, 0.15);
}

.action-icon {
  margin-bottom: 12px;
  transition: transform 0.3s ease;
}

.action-item:hover .action-icon {
  transform: scale(1.1);
}

.charts-row {
  margin-bottom: 24px;
}

.info-row {
  margin-bottom: 24px;
}

.team-activities,
.recruit-list {
  max-height: 400px;
  overflow-y: auto;
  padding-right: 8px;
}

.activity-item,
.recruit-item {
  display: flex;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #f0f2f5;
  transition: background-color 0.3s ease;
}

.activity-item:hover,
.recruit-item:hover {
  background: #f8f9ff;
  border-radius: 8px;
  margin: 0 -8px;
  padding: 16px 8px;
}

.activity-item:last-child,
.recruit-item:last-child {
  border-bottom: none;
}

.activity-avatar {
  margin-right: 16px;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-weight: 600;
  color: #303133;
  margin-bottom: 6px;
  font-size: 14px;
}

.activity-desc {
  color: #606266;
  font-size: 13px;
  margin-bottom: 6px;
  line-height: 1.4;
}

.activity-time {
  color: #909399;
  font-size: 12px;
}

.activity-value {
  text-align: right;
  margin-left: 16px;
}

.value-amount {
  color: #67C23A;
  font-weight: 700;
  font-size: 16px;
}

.recruit-item {
  align-items: flex-start;
}

.recruit-info {
  flex: 1;
}

.recruit-name {
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
  font-size: 14px;
}

.recruit-time {
  margin: 0 20px;
  text-align: center;
  min-width: 100px;
}

.time-label {
  display: block;
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.time-value {
  font-size: 13px;
  color: #606266;
}

.empty-state {
  padding: 60px 20px;
  text-align: center;
}

.promotion-card {
  border-radius: 16px;
  overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .stats-row .el-col {
    margin-bottom: 16px;
  }
  
  .charts-row .el-col {
    margin-bottom: 20px;
  }
}

@media (max-width: 768px) {
  .agent-dashboard {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 20px;
    padding: 20px;
  }
  
  .agent-info {
    flex-direction: column;
    text-align: center;
  }
  
  .info-content {
    margin-left: 0;
    margin-top: 16px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: center;
  }
  
  .action-item {
    padding: 20px 12px;
  }
  
  .recruit-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .recruit-time {
    margin: 0;
  }
}

/* 卡片阴影动画 */
.el-card {
  transition: all 0.3s ease;
}

.el-card:hover {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

/* 招募代理对话框样式 */
.recruit-dialog .el-dialog__body {
  padding: 20px 30px;
}

.recruit-form .form-section {
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.recruit-form .section-title {
  margin: 0 0 20px 0;
  color: #409eff;
  font-size: 16px;
  font-weight: 600;
}

.recruit-form .el-form-item {
  margin-bottom: 20px;
}

.recruit-form .el-textarea__inner {
  resize: vertical;
}

/* 招募记录对话框样式 */
.recruit-records-dialog .el-dialog__body {
  padding: 20px;
}

.recruit-stats {
  margin-bottom: 20px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
}

.recruit-stats .stat-item {
  text-align: center;
}

.recruit-stats .stat-value {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 8px;
}

.recruit-stats .stat-value.pending {
  color: #ffd93d;
}

.recruit-stats .stat-value.approved {
  color: #6bcf7f;
}

.recruit-stats .stat-value.rejected {
  color: #ff6b6b;
}

.recruit-stats .stat-label {
  font-size: 14px;
  opacity: 0.9;
}

.recruit-table .invite-code {
  display: flex;
  align-items: center;
  gap: 8px;
}

.recruit-table .invite-code span {
  font-family: 'Courier New', monospace;
  font-weight: bold;
  color: #409eff;
}
</style>
