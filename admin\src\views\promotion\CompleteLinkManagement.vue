<template>
  <div class="link-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1>🔗 推广链接管理</h1>
        <p>创建和管理推广链接，跟踪点击效果和转化数据</p>
      </div>
      <div class="header-actions">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleDateChange"
          style="margin-right: 12px"
        />
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          创建链接
        </el-button>
        <el-button @click="exportLinks">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
      </div>
    </div>

    <!-- 链接概览统计 -->
    <div class="link-overview">
      <el-row :gutter="20">
        <el-col :span="6" v-for="metric in linkMetrics" :key="metric.key">
          <div class="metric-card">
            <div class="metric-icon" :style="{ backgroundColor: metric.color }">
              <el-icon><component :is="metric.icon" /></el-icon>
            </div>
            <div class="metric-content">
              <div class="metric-value">{{ metric.value }}</div>
              <div class="metric-label">{{ metric.label }}</div>
              <div class="metric-trend" :class="metric.trend">
                <el-icon><component :is="metric.trendIcon" /></el-icon>
                {{ metric.trendText }}
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 筛选和搜索 -->
    <el-card class="filter-card">
      <div class="filter-container">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索链接名称或目标URL"
          style="width: 250px; margin-right: 12px"
          @keyup.enter="handleSearch"
        >
          <template #append>
            <el-button @click="handleSearch">
              <el-icon><Search /></el-icon>
            </el-button>
          </template>
        </el-input>
        
        <el-select v-model="typeFilter" placeholder="链接类型" style="width: 120px; margin-right: 12px">
          <el-option label="全部" value="" />
          <el-option label="群组推广" value="group" />
          <el-option label="分销推广" value="distribution" />
          <el-option label="活动推广" value="activity" />
          <el-option label="其他" value="other" />
        </el-select>

        <el-select v-model="statusFilter" placeholder="链接状态" style="width: 120px; margin-right: 12px">
          <el-option label="全部" value="" />
          <el-option label="活跃" value="active" />
          <el-option label="暂停" value="paused" />
          <el-option label="过期" value="expired" />
        </el-select>

        <el-button @click="resetFilters">重置</el-button>
      </div>
    </el-card>

    <!-- 链接列表 -->
    <el-card class="link-table-card">
      <template #header>
        <div class="table-header">
          <h3>📋 推广链接列表</h3>
          <div class="table-actions">
            <el-button-group>
              <el-button 
                :type="viewMode === 'table' ? 'primary' : ''" 
                @click="viewMode = 'table'"
              >
                <el-icon><List /></el-icon>
                列表视图
              </el-button>
              <el-button 
                :type="viewMode === 'card' ? 'primary' : ''" 
                @click="viewMode = 'card'"
              >
                <el-icon><Grid /></el-icon>
                卡片视图
              </el-button>
            </el-button-group>
          </div>
        </div>
      </template>

      <!-- 表格视图 -->
      <div v-if="viewMode === 'table'">
        <el-table :data="filteredLinks" :loading="loading" stripe>
          <el-table-column type="selection" width="55" />
          <el-table-column prop="name" label="链接名称" min-width="200">
            <template #default="{ row }">
              <div class="link-name">
                <div class="name">{{ row.name }}</div>
                <div class="short-url">
                  <el-link :href="row.shortUrl" target="_blank" type="primary">
                    {{ row.shortUrl }}
                  </el-link>
                  <el-button 
                    type="text" 
                    size="small" 
                    @click="copyToClipboard(row.shortUrl)"
                    class="copy-btn"
                  >
                    <el-icon><DocumentCopy /></el-icon>
                  </el-button>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="链接类型" width="120" align="center">
            <template #default="{ row }">
              <el-tag :type="getTypeColor(row.type)">
                {{ getTypeText(row.type) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="targetUrl" label="目标URL" min-width="200">
            <template #default="{ row }">
              <div class="target-url" :title="row.targetUrl">
                {{ truncateUrl(row.targetUrl) }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="点击统计" width="120" align="center">
            <template #default="{ row }">
              <div class="click-stats">
                <div class="total-clicks">{{ row.totalClicks.toLocaleString() }} 次</div>
                <div class="today-clicks">今日: {{ row.todayClicks }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="转化数据" width="120" align="center">
            <template #default="{ row }">
              <div class="conversion-stats">
                <div class="conversions">{{ row.conversions }} 转化</div>
                <div class="conversion-rate">{{ row.conversionRate.toFixed(1) }}%</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="状态" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="getStatusColor(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createdAt" label="创建时间" width="160" />
          <el-table-column label="操作" width="220" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" link @click="viewAnalytics(row)">
                统计
              </el-button>
              <el-button type="success" link @click="editLink(row)">
                编辑
              </el-button>
              <el-button type="warning" link @click="generateQRCode(row)">
                二维码
              </el-button>
              <el-dropdown @command="handleCommand">
                <el-button type="info" link>
                  更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="`copy-${row.id}`">复制链接</el-dropdown-item>
                    <el-dropdown-item :command="`pause-${row.id}`" v-if="row.status === 'active'">暂停链接</el-dropdown-item>
                    <el-dropdown-item :command="`resume-${row.id}`" v-if="row.status === 'paused'">恢复链接</el-dropdown-item>
                    <el-dropdown-item :command="`delete-${row.id}`" divided>删除链接</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 卡片视图 -->
      <div v-else class="link-cards">
        <el-row :gutter="20">
          <el-col :span="8" v-for="link in filteredLinks" :key="link.id">
            <div class="link-card">
              <div class="card-header">
                <div class="link-info">
                  <h4>{{ link.name }}</h4>
                  <div class="short-url">
                    <el-link :href="link.shortUrl" target="_blank" type="primary">
                      {{ link.shortUrl }}
                    </el-link>
                    <el-button 
                      type="text" 
                      size="small" 
                      @click="copyToClipboard(link.shortUrl)"
                    >
                      <el-icon><DocumentCopy /></el-icon>
                    </el-button>
                  </div>
                </div>
                <div class="link-status">
                  <el-tag :type="getStatusColor(link.status)">
                    {{ getStatusText(link.status) }}
                  </el-tag>
                </div>
              </div>
              
              <div class="card-content">
                <div class="link-stats">
                  <div class="stat-item">
                    <div class="stat-value">{{ link.totalClicks.toLocaleString() }}</div>
                    <div class="stat-label">总点击</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-value">{{ link.todayClicks }}</div>
                    <div class="stat-label">今日点击</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-value">{{ link.conversions }}</div>
                    <div class="stat-label">转化数</div>
                  </div>
                </div>
                
                <div class="link-conversion">
                  <div class="conversion-info">
                    <span>转化率</span>
                    <span>{{ link.conversionRate.toFixed(1) }}%</span>
                  </div>
                  <el-progress 
                    :percentage="Math.min(link.conversionRate, 100)" 
                    :color="getConversionColor(link.conversionRate)"
                  />
                </div>
                
                <div class="link-target">
                  <div class="target-info">
                    <el-icon><Link /></el-icon>
                    <span class="target-url" :title="link.targetUrl">
                      {{ truncateUrl(link.targetUrl) }}
                    </span>
                  </div>
                </div>
              </div>
              
              <div class="card-actions">
                <el-button type="primary" size="small" @click="viewAnalytics(link)">
                  统计
                </el-button>
                <el-button type="success" size="small" @click="editLink(link)">
                  编辑
                </el-button>
                <el-button type="warning" size="small" @click="generateQRCode(link)">
                  二维码
                </el-button>
                <el-button type="danger" size="small" @click="deleteLink(link)">
                  删除
                </el-button>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[12, 24, 48, 96]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 创建/编辑链接对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="isEditing ? '编辑链接' : '创建链接'"
      width="700px"
      @close="handleDialogClose"
    >
      <el-form :model="linkForm" :rules="formRules" ref="formRef" label-width="120px">
        <el-form-item label="链接名称" prop="name">
          <el-input v-model="linkForm.name" placeholder="请输入链接名称" />
        </el-form-item>

        <el-form-item label="目标URL" prop="targetUrl">
          <el-input v-model="linkForm.targetUrl" placeholder="请输入目标URL" />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="链接类型" prop="type">
              <el-select v-model="linkForm.type" placeholder="请选择链接类型">
                <el-option label="群组推广" value="group" />
                <el-option label="分销推广" value="distribution" />
                <el-option label="活动推广" value="activity" />
                <el-option label="其他" value="other" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="有效期" prop="expireDate">
              <el-date-picker
                v-model="linkForm.expireDate"
                type="date"
                placeholder="选择过期时间"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="推广渠道">
          <el-checkbox-group v-model="linkForm.channels">
            <el-checkbox label="微信群" value="wechat" />
            <el-checkbox label="朋友圈" value="moments" />
            <el-checkbox label="短信" value="sms" />
            <el-checkbox label="邮件" value="email" />
            <el-checkbox label="其他" value="other" />
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="备注说明">
          <el-input 
            v-model="linkForm.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入备注说明"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="handleDialogClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="saving">
          {{ isEditing ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 二维码对话框 -->
    <el-dialog
      v-model="showQRCodeDialog"
      title="链接二维码"
      width="400px"
    >
      <div class="qrcode-container">
        <div class="qrcode-info">
          <h4>{{ currentLink.name }}</h4>
          <p>{{ currentLink.shortUrl }}</p>
        </div>
        <div class="qrcode-image">
          <QRCodeCanvas
            ref="qrcodeRef"
            :text="currentLink.shortUrl"
            :size="200"
            :auto-generate="false"
            @generated="onQRCodeGenerated"
            @error="onQRCodeError"
          />
        </div>
        <div class="qrcode-actions">
          <el-button @click="downloadQRCode">下载二维码</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Download, Search, List, Grid, Link, DocumentCopy, ArrowDown,
  TrendCharts, User, View, Trophy, ArrowUp, ArrowDown as ArrowDownIcon
} from '@element-plus/icons-vue'
import { exportLinkData } from '@/utils/promotionExportUtils'
import QRCode from 'qrcode'
import QRCodeCanvas from '@/components/common/QRCodeCanvas.vue'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const showCreateDialog = ref(false)
const showQRCodeDialog = ref(false)
const isEditing = ref(false)
const editingId = ref(null)
const viewMode = ref('table')
const dateRange = ref([])
const searchKeyword = ref('')
const typeFilter = ref('')
const statusFilter = ref('')
const qrcodeRef = ref(null)
const currentLink = ref({})

const pagination = reactive({
  page: 1,
  pageSize: 12,
  total: 0
})

// 链接概览指标
const linkMetrics = ref([
  {
    key: 'totalLinks',
    label: '总链接数',
    value: '156',
    color: '#409EFF',
    icon: Link,
    trend: 'up',
    trendIcon: ArrowUp,
    trendText: '+12 本月'
  },
  {
    key: 'totalClicks',
    label: '总点击量',
    value: '234,567',
    color: '#67C23A',
    icon: View,
    trend: 'up',
    trendIcon: ArrowUp,
    trendText: '+18.5%'
  },
  {
    key: 'uniqueVisitors',
    label: '独立访客',
    value: '45,678',
    color: '#E6A23C',
    icon: User,
    trend: 'up',
    trendIcon: ArrowUp,
    trendText: '+15.2%'
  },
  {
    key: 'conversionRate',
    label: '平均转化率',
    value: '12.8%',
    color: '#F56C6C',
    icon: Trophy,
    trend: 'down',
    trendIcon: ArrowDownIcon,
    trendText: '-2.3%'
  }
])

// 链接数据
const linkList = ref([
  {
    id: 1,
    name: '春季活动推广链接',
    shortUrl: 'https://t.cn/A6X9Y8Z1',
    targetUrl: 'https://example.com/spring-promotion?utm_source=wechat&utm_medium=link&utm_campaign=spring2024',
    type: 'activity',
    status: 'active',
    totalClicks: 8765,
    todayClicks: 234,
    conversions: 876,
    conversionRate: 10.0,
    channels: ['wechat', 'moments'],
    createdAt: '2024-03-01 10:30:00',
    expireDate: '2024-04-30',
    description: '春季促销活动专用推广链接'
  },
  {
    id: 2,
    name: '分销推广-产品A',
    shortUrl: 'https://t.cn/A6X9Y8Z2',
    targetUrl: 'https://example.com/product-a?ref=distributor&id=12345',
    type: 'distribution',
    status: 'active',
    totalClicks: 5432,
    todayClicks: 156,
    conversions: 543,
    conversionRate: 10.0,
    channels: ['wechat', 'sms'],
    createdAt: '2024-03-05 14:20:00',
    expireDate: '2024-06-30',
    description: '产品A分销专用链接'
  },
  {
    id: 3,
    name: '群组推广链接001',
    shortUrl: 'https://t.cn/A6X9Y8Z3',
    targetUrl: 'https://example.com/group-special?group=vip&code=GROUP001',
    type: 'group',
    status: 'paused',
    totalClicks: 3456,
    todayClicks: 0,
    conversions: 345,
    conversionRate: 10.0,
    channels: ['wechat'],
    createdAt: '2024-02-20 09:15:00',
    expireDate: '2024-05-20',
    description: 'VIP群组专属推广链接'
  },
  {
    id: 4,
    name: '邮件营销链接',
    shortUrl: 'https://t.cn/A6X9Y8Z4',
    targetUrl: 'https://example.com/email-campaign?utm_source=email&utm_campaign=newsletter',
    type: 'other',
    status: 'active',
    totalClicks: 2345,
    todayClicks: 89,
    conversions: 234,
    conversionRate: 10.0,
    channels: ['email'],
    createdAt: '2024-03-10 16:45:00',
    expireDate: '2024-12-31',
    description: '邮件营销专用链接'
  }
])

// 表单数据
const linkForm = reactive({
  name: '',
  targetUrl: '',
  type: '',
  expireDate: '',
  channels: [],
  description: ''
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入链接名称', trigger: 'blur' }
  ],
  targetUrl: [
    { required: true, message: '请输入目标URL', trigger: 'blur' },
    { type: 'url', message: '请输入有效的URL', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择链接类型', trigger: 'change' }
  ]
}

const formRef = ref(null)

// 计算属性
const filteredLinks = computed(() => {
  let data = linkList.value

  if (searchKeyword.value) {
    data = data.filter(item =>
      item.name.includes(searchKeyword.value) ||
      item.targetUrl.includes(searchKeyword.value)
    )
  }

  if (typeFilter.value) {
    data = data.filter(item => item.type === typeFilter.value)
  }

  if (statusFilter.value) {
    data = data.filter(item => item.status === statusFilter.value)
  }

  return data
})

// 方法
const getTypeColor = (type) => {
  const colors = {
    group: 'primary',
    distribution: 'success',
    activity: 'warning',
    other: 'info'
  }
  return colors[type] || 'info'
}

const getTypeText = (type) => {
  const texts = {
    group: '群组推广',
    distribution: '分销推广',
    activity: '活动推广',
    other: '其他'
  }
  return texts[type] || '未知'
}

const getStatusColor = (status) => {
  const colors = {
    active: 'success',
    paused: 'warning',
    expired: 'danger'
  }
  return colors[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    active: '活跃',
    paused: '暂停',
    expired: '过期'
  }
  return texts[status] || '未知'
}

const getConversionColor = (rate) => {
  if (rate >= 15) return '#67C23A'
  if (rate >= 10) return '#E6A23C'
  return '#F56C6C'
}

const truncateUrl = (url) => {
  if (!url) return ''
  return url.length > 50 ? url.substring(0, 50) + '...' : url
}

const handleDateChange = (dates) => {
  console.log('日期范围变更:', dates)
  refreshData()
}

const handleSearch = () => {
  console.log('搜索:', searchKeyword.value)
}

const resetFilters = () => {
  searchKeyword.value = ''
  typeFilter.value = ''
  statusFilter.value = ''
}

const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    ElMessage.success('数据已刷新')
  }, 1000)
}

const exportLinks = () => {
  try {
    if (linkList.value.length === 0) {
      ElMessage.warning('没有数据可导出')
      return
    }

    exportLinkData(linkList.value, 'csv', `推广链接数据_${new Date().toISOString().split('T')[0]}`)
    ElMessage.success(`已导出 ${linkList.value.length} 条链接数据`)
  } catch (error) {
    ElMessage.error('导出失败: ' + error.message)
  }
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.page = 1
}

const handleCurrentChange = (page) => {
  pagination.page = page
}

const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('链接已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const viewAnalytics = (row) => {
  router.push(`/admin/promotion/links/${row.id}/analytics`)
}

const editLink = (row) => {
  isEditing.value = true
  editingId.value = row.id

  Object.assign(linkForm, {
    name: row.name,
    targetUrl: row.targetUrl,
    type: row.type,
    expireDate: row.expireDate,
    channels: [...row.channels],
    description: row.description
  })

  showCreateDialog.value = true
}

const generateQRCode = async (row) => {
  currentLink.value = { ...row }
  showQRCodeDialog.value = true

  // 等待对话框打开后生成二维码
  await nextTick()
  if (qrcodeRef.value) {
    qrcodeRef.value.generateQRCode()
  }
}

// 二维码生成成功回调
const onQRCodeGenerated = (data) => {
  console.log('二维码生成成功:', data)
}

// 二维码生成失败回调
const onQRCodeError = (error) => {
  console.error('二维码生成失败:', error)
}

const downloadQRCode = () => {
  if (qrcodeRef.value) {
    qrcodeRef.value.download(`${currentLink.value.name}_qrcode.png`)
  }
}

const deleteLink = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除链接"${row.name}"吗？`,
      '确认删除',
      { type: 'warning' }
    )

    const index = linkList.value.findIndex(item => item.id === row.id)
    if (index !== -1) {
      linkList.value.splice(index, 1)
      ElMessage.success('删除成功')
    }
  } catch (error) {
    // 用户取消删除
  }
}

const handleCommand = (command) => {
  const [action, linkId] = command.split('-')
  const id = parseInt(linkId)
  const link = linkList.value.find(l => l.id === id)

  if (!link) return

  switch (action) {
    case 'copy':
      copyToClipboard(link.shortUrl)
      break
    case 'pause':
      updateLinkStatus(link, 'paused')
      break
    case 'resume':
      updateLinkStatus(link, 'active')
      break
    case 'delete':
      deleteLink(link)
      break
  }
}

const updateLinkStatus = (link, status) => {
  const index = linkList.value.findIndex(item => item.id === link.id)
  if (index !== -1) {
    linkList.value[index].status = status
    const action = status === 'paused' ? '暂停' : '恢复'
    ElMessage.success(`${action}成功`)
  }
}

const handleDialogClose = () => {
  showCreateDialog.value = false
  isEditing.value = false
  editingId.value = null
  resetForm()
}

const resetForm = () => {
  Object.assign(linkForm, {
    name: '',
    targetUrl: '',
    type: '',
    expireDate: '',
    channels: [],
    description: ''
  })

  if (formRef.value) {
    formRef.value.resetFields()
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    saving.value = true

    // 模拟API调用
    setTimeout(() => {
      if (isEditing.value) {
        const index = linkList.value.findIndex(item => item.id === editingId.value)
        if (index !== -1) {
          Object.assign(linkList.value[index], {
            ...linkForm,
            totalClicks: linkList.value[index].totalClicks,
            todayClicks: linkList.value[index].todayClicks,
            conversions: linkList.value[index].conversions,
            conversionRate: linkList.value[index].conversionRate
          })
        }
        ElMessage.success('链接更新成功')
      } else {
        const newLink = {
          id: Date.now(),
          ...linkForm,
          shortUrl: `https://t.cn/A${Math.random().toString(36).substr(2, 6)}`,
          status: 'active',
          totalClicks: 0,
          todayClicks: 0,
          conversions: 0,
          conversionRate: 0,
          createdAt: new Date().toLocaleString('zh-CN')
        }
        linkList.value.unshift(newLink)
        ElMessage.success('链接创建成功')
      }

      saving.value = false
      handleDialogClose()
    }, 1000)

  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 生命周期
onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.link-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
}

.header-left h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #303133;
}

.header-left p {
  margin: 0;
  color: #909399;
}

.header-actions {
  display: flex;
  align-items: center;
}

.link-overview {
  margin-bottom: 24px;
}

.metric-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.metric-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  margin-right: 16px;
}

.metric-content {
  flex: 1;
}

.metric-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.metric-label {
  font-size: 14px;
  color: #909399;
  margin-bottom: 4px;
}

.metric-trend {
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.metric-trend.up {
  color: #67c23a;
}

.metric-trend.down {
  color: #f56c6c;
}

.filter-card {
  margin-bottom: 20px;
}

.filter-container {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.link-name .name {
  font-weight: 500;
  margin-bottom: 4px;
}

.link-name .short-url {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.copy-btn {
  padding: 0;
  min-height: auto;
}

.target-url {
  color: #606266;
  font-size: 13px;
}

.click-stats .total-clicks {
  font-weight: 500;
  margin-bottom: 2px;
}

.click-stats .today-clicks {
  font-size: 12px;
  color: #909399;
}

.conversion-stats .conversions {
  font-weight: 500;
  margin-bottom: 2px;
}

.conversion-stats .conversion-rate {
  font-size: 12px;
  color: #67c23a;
}

.link-cards {
  margin-bottom: 20px;
}

.link-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20px 20px 0 20px;
}

.link-info h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #303133;
}

.link-info .short-url {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.card-content {
  padding: 20px;
}

.link-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 20px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.link-conversion {
  margin-bottom: 16px;
}

.conversion-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.link-target {
  margin-bottom: 16px;
}

.target-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #606266;
}

.target-url {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.card-actions {
  padding: 0 20px 20px 20px;
  display: flex;
  gap: 8px;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.qrcode-container {
  text-align: center;
}

.qrcode-info h4 {
  margin: 0 0 8px 0;
  color: #303133;
}

.qrcode-info p {
  margin: 0 0 20px 0;
  color: #606266;
  font-size: 14px;
}

.qrcode-image {
  margin-bottom: 20px;
}

.qrcode {
  display: inline-block;
}
</style>
