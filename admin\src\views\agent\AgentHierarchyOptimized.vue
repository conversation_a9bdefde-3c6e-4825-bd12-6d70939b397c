<template>
  <div class="modern-agent-hierarchy">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <div class="header-icon">
            <el-icon size="24"><Connection /></el-icon>
          </div>
          <div class="header-text">
            <h1>团队层级管理</h1>
            <p>管理代理商团队结构和层级关系，优化团队组织架构</p>
          </div>
        </div>
        <div class="header-actions">
          <el-button @click="handleExport" class="action-btn secondary">
            <el-icon><Download /></el-icon>
            导出数据
          </el-button>
          <el-button @click="showHelpDialog = true" class="action-btn secondary">
            <el-icon><QuestionFilled /></el-icon>
            功能说明
          </el-button>
          <el-button @click="showAddAgentDialog" class="action-btn secondary">
            <el-icon><Plus /></el-icon>
            添加下级代理
          </el-button>
          <el-button type="primary" @click="refreshHierarchy" class="action-btn primary" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片区域 -->
    <div class="stats-section">
      <div class="stats-container" v-loading="statsLoading">
        <div class="stat-card" v-for="stat in hierarchyStatCards" :key="stat.key">
          <div class="stat-icon" :style="{ background: stat.color }">
            <el-icon size="20">
              <component :is="stat.icon" />
            </el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stat.value }}</div>
            <div class="stat-label">{{ stat.label }}</div>
          </div>
          <div class="stat-trend" :class="stat.trend">
            <el-icon size="14">
              <component :is="stat.trendIcon" />
            </el-icon>
            <span>{{ stat.change }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 层级树形结构 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <div class="header-left">
            <h3>团队层级结构</h3>
            <el-tag size="small" type="info">共 {{ hierarchyStats.totalAgents || 0 }} 人</el-tag>
          </div>
          <div class="header-right">
            <el-button-group>
              <el-button size="small" :type="viewMode === 'tree' ? 'primary' : ''" @click="viewMode = 'tree'">
                <el-icon><Share /></el-icon>
              </el-button>
              <el-button size="small" :type="viewMode === 'table' ? 'primary' : ''" @click="viewMode = 'table'">
                <el-icon><List /></el-icon>
              </el-button>
            </el-button-group>
          </div>
        </div>
      </template>

      <!-- 树形视图 -->
      <div v-if="viewMode === 'tree'" class="tree-view">
        <el-tree
          ref="hierarchyTree"
          :data="hierarchyData"
          :props="treeProps"
          :expand-on-click-node="false"
          :default-expand-all="false"
          node-key="id"
          class="modern-tree"
          v-loading="loading"
        >
          <template #default="{ node, data }">
            <div class="tree-node">
              <div class="node-info">
                <el-avatar :size="32" :src="data.avatar" class="node-avatar">
                  {{ data.agent_name && data.agent_name.charAt(0) }}
                </el-avatar>
                <div class="node-details">
                  <div class="node-name">{{ data.agent_name }}</div>
                  <div class="node-meta">
                    <el-tag :type="getLevelTagType(data.agent_level)" size="small">
                      {{ getLevelText(data.agent_level) }}
                    </el-tag>
                    <span class="node-code">{{ data.agent_code }}</span>
                  </div>
                </div>
              </div>
              <div class="node-stats">
                <div class="stat-item">
                  <span class="stat-label">下级:</span>
                  <span class="stat-value">{{ data.children_count || 0 }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">佣金:</span>
                  <span class="stat-value">¥{{ data.total_commission || 0 }}</span>
                </div>
              </div>
              <div class="node-actions">
                <el-button size="small" type="primary" link @click="viewAgentDetail(data)">
                  <el-icon><View /></el-icon>
                  详情
                </el-button>
                <el-button size="small" type="success" link @click="addSubAgent(data)">
                  <el-icon><Plus /></el-icon>
                  添加下级
                </el-button>
                <el-dropdown @command="handleNodeCommand" trigger="click">
                  <el-button size="small" type="info" link>
                    <el-icon><More /></el-icon>
                    更多
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item :command="{ action: 'edit', data }">
                        <el-icon><Edit /></el-icon>
                        编辑信息
                      </el-dropdown-item>
                      <el-dropdown-item :command="{ action: 'commission', data }">
                        <el-icon><Money /></el-icon>
                        佣金管理
                      </el-dropdown-item>
                      <el-dropdown-item :command="{ action: 'performance', data }">
                        <el-icon><TrendCharts /></el-icon>
                        业绩分析
                      </el-dropdown-item>
                      <el-dropdown-item :command="{ action: 'transfer', data }" divided>
                        <el-icon><Switch /></el-icon>
                        转移代理
                      </el-dropdown-item>
                      <el-dropdown-item :command="{ action: 'disable', data }" v-if="data.status === 'active'">
                        <el-icon><CircleClose /></el-icon>
                        停用代理
                      </el-dropdown-item>
                      <el-dropdown-item :command="{ action: 'enable', data }" v-else>
                        <el-icon><CircleCheck /></el-icon>
                        启用代理
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>
          </template>
        </el-tree>
      </div>

      <!-- 表格视图 -->
      <div v-else class="table-view">
        <el-table :data="flatHierarchyData" v-loading="loading" class="modern-table">
          <el-table-column prop="agent_name" label="代理商" min-width="200">
            <template #default="{ row }">
              <div class="agent-info">
                <el-avatar :size="32" :src="row.avatar">
                  {{ row.agent_name && row.agent_name.charAt(0) }}
                </el-avatar>
                <div class="agent-details">
                  <div class="agent-name">{{ row.agent_name }}</div>
                  <div class="agent-code">{{ row.agent_code }}</div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="level_path" label="层级路径" min-width="150">
            <template #default="{ row }">
              <div class="level-path">
                <span v-for="(level, index) in row.level_path" :key="index" class="level-item">
                  {{ level }}
                  <el-icon v-if="index < row.level_path.length - 1"><ArrowRight /></el-icon>
                </span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="agent_level" label="代理等级" width="120">
            <template #default="{ row }">
              <el-tag :type="getLevelTagType(row.agent_level)" size="small">
                {{ getLevelText(row.agent_level) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="children_count" label="下级数量" width="100" align="center">
            <template #default="{ row }">
              <el-badge :value="row.children_count || 0" :max="99" class="children-badge">
                <el-icon><Avatar /></el-icon>
              </el-badge>
            </template>
          </el-table-column>
          <el-table-column prop="total_commission" label="累计佣金" width="120" align="right">
            <template #default="{ row }">
              <span class="commission-amount">¥{{ row.total_commission || 0 }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="row.status === 'active' ? 'success' : 'danger'" size="small">
                {{ row.status === 'active' ? '正常' : '停用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="加入时间" width="120">
            <template #default="{ row }">
              {{ formatDate(row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button size="small" type="primary" link @click="viewAgentDetail(row)">
                详情
              </el-button>
              <el-button size="small" type="success" link @click="addSubAgent(row)">
                添加下级
              </el-button>
              <el-dropdown @command="handleNodeCommand" trigger="click">
                <el-button size="small" type="info" link>
                  更多<el-icon><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="{ action: 'edit', data: row }">编辑</el-dropdown-item>
                    <el-dropdown-item :command="{ action: 'commission', data: row }">佣金</el-dropdown-item>
                    <el-dropdown-item :command="{ action: 'performance', data: row }">业绩</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
        
        <div class="pagination">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="hierarchyStats.totalAgents"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>

    <!-- 添加代理商对话框 -->
    <el-dialog v-model="showAddDialog" title="添加下级代理" width="600px" class="add-agent-dialog">
      <el-form :model="addForm" :rules="addRules" ref="addFormRef" label-width="100px">
        <el-form-item label="代理商姓名" prop="agent_name">
          <el-input v-model="addForm.agent_name" placeholder="请输入代理商姓名" />
        </el-form-item>
        <el-form-item label="手机号码" prop="phone">
          <el-input v-model="addForm.phone" placeholder="请输入手机号码" />
        </el-form-item>
        <el-form-item label="邮箱地址" prop="email">
          <el-input v-model="addForm.email" placeholder="请输入邮箱地址" />
        </el-form-item>
        <el-form-item label="代理等级" prop="agent_level">
          <el-select v-model="addForm.agent_level" placeholder="请选择代理等级">
            <el-option label="初级代理" value="junior" />
            <el-option label="中级代理" value="intermediate" />
            <el-option label="高级代理" value="senior" />
            <el-option label="金牌代理" value="gold" />
          </el-select>
        </el-form-item>
        <el-form-item label="上级代理" prop="parent_id">
          <el-cascader
            v-model="addForm.parent_id"
            :options="agentOptions"
            :props="cascaderProps"
            placeholder="请选择上级代理"
            clearable
          />
        </el-form-item>
        <el-form-item label="备注信息">
          <el-input v-model="addForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showAddDialog = false">取消</el-button>
          <el-button type="primary" @click="handleAddAgent" :loading="addLoading">
            确定添加
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 代理商详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="代理商详情" width="800px" class="detail-dialog">
      <div v-if="selectedAgent" class="agent-detail">
        <div class="detail-header">
          <el-avatar :size="80" :src="selectedAgent.avatar">
            {{ selectedAgent.agent_name && selectedAgent.agent_name.charAt(0) }}
          </el-avatar>
          <div class="agent-info">
            <h3>{{ selectedAgent.agent_name }}</h3>
            <p class="agent-code">代理编号: {{ selectedAgent.agent_code }}</p>
            <el-tag :type="getLevelTagType(selectedAgent.agent_level)">
              {{ getLevelText(selectedAgent.agent_level) }}
            </el-tag>
          </div>
        </div>

        <el-tabs v-model="activeTab" class="detail-tabs">
          <el-tab-pane label="基本信息" name="basic">
            <div class="info-grid">
              <div class="info-item">
                <label>手机号码:</label>
                <span>{{ selectedAgent.phone || '-' }}</span>
              </div>
              <div class="info-item">
                <label>邮箱地址:</label>
                <span>{{ selectedAgent.email || '-' }}</span>
              </div>
              <div class="info-item">
                <label>加入时间:</label>
                <span>{{ formatDate(selectedAgent.created_at) }}</span>
              </div>
              <div class="info-item">
                <label>状态:</label>
                <el-tag :type="selectedAgent.status === 'active' ? 'success' : 'danger'" size="small">
                  {{ selectedAgent.status === 'active' ? '正常' : '停用' }}
                </el-tag>
              </div>
              <div class="info-item">
                <label>直属下级:</label>
                <span>{{ selectedAgent.children_count || 0 }}人</span>
              </div>
              <div class="info-item">
                <label>团队总人数:</label>
                <span>{{ selectedAgent.team_count || 0 }}人</span>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="业绩统计" name="performance">
            <div class="performance-stats">
              <div class="stat-row">
                <div class="stat-item">
                  <div class="stat-value">¥{{ selectedAgent.total_commission || 0 }}</div>
                  <div class="stat-label">累计佣金</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">¥{{ selectedAgent.month_commission || 0 }}</div>
                  <div class="stat-label">本月佣金</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">{{ selectedAgent.total_orders || 0 }}</div>
                  <div class="stat-label">累计订单</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">{{ selectedAgent.month_orders || 0 }}</div>
                  <div class="stat-label">本月订单</div>
                </div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="团队结构" name="team">
            <div class="team-structure">
              <p>团队层级结构图表将在这里显示</p>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>

    <!-- 编辑代理商对话框 -->
    <el-dialog v-model="showEditDialog" title="编辑代理商" width="600px" class="edit-agent-dialog">
      <el-form :model="editForm" :rules="editRules" ref="editFormRef" label-width="100px">
        <el-form-item label="代理商姓名" prop="agent_name">
          <el-input v-model="editForm.agent_name" placeholder="请输入代理商姓名" />
        </el-form-item>
        <el-form-item label="手机号码" prop="phone">
          <el-input v-model="editForm.phone" placeholder="请输入手机号码" />
        </el-form-item>
        <el-form-item label="邮箱地址" prop="email">
          <el-input v-model="editForm.email" placeholder="请输入邮箱地址" />
        </el-form-item>
        <el-form-item label="代理等级" prop="agent_level">
          <el-select v-model="editForm.agent_level" placeholder="请选择代理等级" style="width: 100%">
            <el-option label="初级代理" value="junior" />
            <el-option label="中级代理" value="intermediate" />
            <el-option label="高级代理" value="senior" />
            <el-option label="金牌代理" value="gold" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="editForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showEditDialog = false">取消</el-button>
          <el-button type="primary" @click="handleEditSubmit" :loading="editLoading">
            保存修改
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 佣金管理对话框 -->
    <el-dialog v-model="showCommissionDialog" title="佣金管理" width="800px" class="commission-dialog">
      <div v-if="selectedAgent" class="commission-content">
        <div class="commission-header">
          <h3>{{ selectedAgent.agent_name }} 的佣金信息</h3>
        </div>
        <el-row :gutter="20" class="commission-stats">
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-value">¥15,680.50</div>
              <div class="stat-label">总佣金</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-value">¥12,340.00</div>
              <div class="stat-label">可用佣金</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-value">¥3,340.50</div>
              <div class="stat-label">已提现</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-value">¥850.00</div>
              <div class="stat-label">待结算</div>
            </div>
          </el-col>
        </el-row>
        <div class="commission-records">
          <h4>最近佣金记录</h4>
          <el-table :data="mockCommissionRecords" style="width: 100%">
            <el-table-column prop="order_no" label="订单号" width="150" />
            <el-table-column prop="amount" label="佣金金额" width="120">
              <template #default="scope">
                <span class="amount">¥{{ scope.row.amount }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="rate" label="佣金比例" width="100">
              <template #default="scope">
                <span>{{ scope.row.rate }}%</span>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="scope.row.status === 'settled' ? 'success' : 'warning'">
                  {{ scope.row.status === 'settled' ? '已结算' : '待结算' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="created_at" label="时间" />
          </el-table>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showCommissionDialog = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 业绩分析对话框 -->
    <el-dialog v-model="showPerformanceDialog" title="业绩分析" width="800px" class="performance-dialog">
      <div v-if="selectedAgent" class="performance-content">
        <div class="performance-header">
          <h3>{{ selectedAgent.agent_name }} 的业绩分析</h3>
        </div>
        <el-row :gutter="20" class="performance-stats">
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-value">156</div>
              <div class="stat-label">总订单数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-value">¥45,680</div>
              <div class="stat-label">总销售额</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-value">12.8%</div>
              <div class="stat-label">转化率</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-value">89</div>
              <div class="stat-label">客户数量</div>
            </div>
          </el-col>
        </el-row>
        <div class="performance-records">
          <h4>最近订单记录</h4>
          <el-table :data="mockPerformanceRecords" style="width: 100%">
            <el-table-column prop="order_no" label="订单号" width="150" />
            <el-table-column prop="customer_name" label="客户姓名" width="120" />
            <el-table-column prop="amount" label="订单金额" width="120">
              <template #default="scope">
                <span class="amount">¥{{ scope.row.amount }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="scope.row.status === 'completed' ? 'success' : 'warning'">
                  {{ scope.row.status === 'completed' ? '已完成' : '处理中' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="created_at" label="时间" />
          </el-table>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showPerformanceDialog = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 转移代理对话框 -->
    <el-dialog v-model="showTransferDialog" title="转移代理商" width="600px" class="transfer-dialog">
      <el-form :model="transferForm" :rules="transferRules" ref="transferFormRef" label-width="120px">
        <el-form-item label="当前代理商">
          <el-input :value="selectedAgent?.agent_name" disabled />
        </el-form-item>
        <el-form-item label="转移到" prop="new_parent_id">
          <el-select v-model="transferForm.new_parent_id" placeholder="请选择新的上级代理商" style="width: 100%">
            <el-option label="总部直属" :value="null" />
            <el-option label="张三 (高级代理)" value="1" />
            <el-option label="李四 (金牌代理)" value="2" />
            <el-option label="王五 (中级代理)" value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="转移原因" prop="reason">
          <el-input v-model="transferForm.reason" type="textarea" :rows="4" placeholder="请输入转移原因" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showTransferDialog = false">取消</el-button>
          <el-button type="primary" @click="handleTransferSubmit" :loading="transferLoading">
            确认转移
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh, Plus, Avatar, Connection, TrendCharts, Money, Download, QuestionFilled,
  View, Edit, More, Switch, CircleClose, CircleCheck, Share, List,
  ArrowRight, ArrowDown, ArrowUp
} from '@element-plus/icons-vue'
import { agentApi } from '@/api/agent'

// 页面状态
const loading = ref(false)
const statsLoading = ref(true)
const viewMode = ref('tree')
const showAddDialog = ref(false)
const showDetailDialog = ref(false)
const showEditDialog = ref(false)
const showCommissionDialog = ref(false)
const showPerformanceDialog = ref(false)
const showTransferDialog = ref(false)
const showHelpDialog = ref(false)
const addLoading = ref(false)
const selectedAgent = ref(null)
const activeTab = ref('basic')
const currentPage = ref(1)
const pageSize = ref(20)

// 帮助对话框相关
const activeGuides = ref(['add-agent'])

// 层级数据
const hierarchyData = ref([])
const hierarchyStats = reactive({
  totalAgents: 0,
  directChildren: 0,
  maxLevel: 0,
  totalCommission: 0
})

// 团队层级统计卡片数据 - 与其他页面保持一致的设计
const hierarchyStatCards = ref([
  {
    key: 'totalAgents',
    label: '团队总人数',
    value: '0',
    icon: 'Avatar',
    color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '+8人'
  },
  {
    key: 'directChildren',
    label: '直属下级',
    value: '0',
    icon: 'Connection',
    color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '+3人'
  },
  {
    key: 'maxLevel',
    label: '最大层级',
    value: '0',
    icon: 'TrendCharts',
    color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '5级'
  },
  {
    key: 'totalCommission',
    label: '团队总佣金',
    value: '¥0',
    icon: 'Money',
    color: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '+12.8%'
  }
])

// 树形组件配置
const treeProps = {
  children: 'children',
  label: 'agent_name'
}

// 级联选择器配置
const cascaderProps = {
  value: 'id',
  label: 'agent_name',
  children: 'children'
}

// 添加代理表单
const addForm = reactive({
  agent_name: '',
  phone: '',
  email: '',
  agent_level: '',
  parent_id: null,
  remark: ''
})

// 编辑代理表单
const editForm = ref({
  agent_name: '',
  phone: '',
  email: '',
  agent_level: '',
  remark: ''
})

// 转移代理表单
const transferForm = ref({
  agent_id: null,
  new_parent_id: null,
  reason: ''
})

// 表单验证规则
const editRules = {
  agent_name: [
    { required: true, message: '请输入代理商姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '姓名长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  agent_level: [
    { required: true, message: '请选择代理等级', trigger: 'change' }
  ]
}

const transferRules = {
  reason: [
    { required: true, message: '请输入转移原因', trigger: 'blur' },
    { min: 10, max: 200, message: '转移原因长度在 10 到 200 个字符', trigger: 'blur' }
  ]
}

// 表单引用
const editFormRef = ref(null)
const transferFormRef = ref(null)
const editLoading = ref(false)
const transferLoading = ref(false)

// 模拟数据
const mockCommissionRecords = ref([
  {
    order_no: 'ORD202401001',
    amount: 150.00,
    rate: 15,
    status: 'settled',
    created_at: '2024-01-15 10:30:00'
  },
  {
    order_no: 'ORD202401002',
    amount: 80.00,
    rate: 8,
    status: 'pending',
    created_at: '2024-01-16 14:20:00'
  }
])

const mockPerformanceRecords = ref([
  {
    order_no: 'ORD202401001',
    customer_name: '张三',
    amount: 299.00,
    status: 'completed',
    created_at: '2024-01-15 10:30:00'
  },
  {
    order_no: 'ORD202401002',
    customer_name: '李四',
    amount: 199.00,
    status: 'processing',
    created_at: '2024-01-16 14:20:00'
  }
])

const addRules = {
  agent_name: [
    { required: true, message: '请输入代理商姓名', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号码', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  agent_level: [
    { required: true, message: '请选择代理等级', trigger: 'change' }
  ]
}

const addFormRef = ref()

// 计算属性
const flatHierarchyData = computed(() => {
  const flattenData = (data, level = 1, parentPath = []) => {
    let result = []
    data.forEach(item => {
      const currentPath = [...parentPath, item.agent_name]
      const flatItem = {
        ...item,
        level,
        level_path: currentPath
      }
      result.push(flatItem)
      if (item.children && item.children.length > 0) {
        result = result.concat(flattenData(item.children, level + 1, currentPath))
      }
    })
    return result
  }
  return flattenData(hierarchyData.value)
})

const agentOptions = computed(() => {
  return hierarchyData.value
})

// 方法
const refreshHierarchy = async () => {
  await Promise.all([
    loadHierarchyData(),
    loadStats()
  ])
  ElMessage.success('数据刷新成功')
}

const handleExport = async () => {
  try {
    console.log('📤 开始导出团队数据...')
    
    // 调用API导出数据
    const response = await agentApi.export({
      format: 'xlsx',
      include_children: true
    })
    
    // 创建下载链接
    const blob = new Blob([response], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })
    
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `代理商层级数据_${new Date().toISOString().slice(0, 10)}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('团队数据导出成功')
    console.log('✅ 团队数据导出完成')
  } catch (error) {
    console.error('❌ 导出失败:', error)
    ElMessage.error('导出失败: ' + (error.message || '网络错误'))
  }
}

const handleSizeChange = (size) => {
  pageSize.value = size
  loadHierarchyData()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadHierarchyData()
}

const loadStats = async () => {
  try {
    statsLoading.value = true
    console.log('加载团队统计数据...')
    
    // 调用真实API获取统计数据
    const response = await agentApi.getHierarchyStats()
    
    if (response.success) {
      const stats = response.data
      hierarchyStats.totalAgents = stats.total_agents || 0
      hierarchyStats.directChildren = stats.direct_children || 0
      hierarchyStats.maxLevel = stats.max_level || 0
      hierarchyStats.totalCommission = stats.total_commission || 0
      
      // 更新统计卡片数据
      hierarchyStatCards.value[0].value = hierarchyStats.totalAgents.toString()
      hierarchyStatCards.value[1].value = hierarchyStats.directChildren.toString()
      hierarchyStatCards.value[2].value = hierarchyStats.maxLevel.toString()
      hierarchyStatCards.value[3].value = '¥' + (hierarchyStats.totalCommission || 0).toLocaleString()
      
      console.log('团队统计数据加载完成')
    } else {
      throw new Error(response.message || '获取统计数据失败')
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
    ElMessage.error('加载统计数据失败: ' + (error.message || '网络错误'))
    
    // 使用默认值
    hierarchyStatCards.value[0].value = hierarchyStats.totalAgents.toString()
    hierarchyStatCards.value[1].value = hierarchyStats.directChildren.toString()
    hierarchyStatCards.value[2].value = hierarchyStats.maxLevel.toString()
    hierarchyStatCards.value[3].value = '¥' + (hierarchyStats.totalCommission || 0).toLocaleString()
  } finally {
    statsLoading.value = false
  }
}

const showAddAgentDialog = () => {
  resetAddForm()
  showAddDialog.value = true
}

const resetAddForm = () => {
  Object.assign(addForm, {
    agent_name: '',
    phone: '',
    email: '',
    agent_level: '',
    parent_id: null,
    remark: ''
  })
  addFormRef.value?.clearValidate()
}

const handleAddAgent = async () => {
  try {
    await addFormRef.value.validate()
    addLoading.value = true

    // 调用API添加代理商
    const response = await agentApi.create({
      agent_name: addForm.agent_name,
      phone: addForm.phone,
      email: addForm.email,
      agent_level: addForm.agent_level,
      parent_id: addForm.parent_id?.[0] || null,
      remark: addForm.remark
    })

    if (response.success) {
      ElMessage.success('添加代理商成功')
      showAddDialog.value = false
      await loadHierarchyData()
    } else {
      throw new Error(response.message || '添加代理商失败')
    }
  } catch (error) {
    console.error('添加代理商失败:', error)
    if (error !== false) { // 不是表单验证错误
      ElMessage.error('添加代理商失败: ' + (error.message || '网络错误'))
    }
  } finally {
    addLoading.value = false
  }
}

const viewAgentDetail = (agent) => {
  selectedAgent.value = agent
  activeTab.value = 'basic'
  showDetailDialog.value = true
}

const addSubAgent = (parentAgent) => {
  resetAddForm()
  addForm.parent_id = [parentAgent.id]
  showAddDialog.value = true
}

// 新增对话框方法
const showEditAgentDialog = (agent) => {
  try {
    console.log('📝 显示编辑代理商对话框:', agent)

    if (!agent) {
      console.error('❌ 代理商数据为空')
      ElMessage.error('无法编辑：代理商数据为空')
      return
    }

    selectedAgent.value = agent
    editForm.value = {
      agent_name: agent.agent_name || '',
      phone: agent.phone || '',
      email: agent.email || '',
      agent_level: agent.agent_level || '',
      remark: agent.remark || ''
    }
    showEditDialog.value = true

    console.log('✅ 编辑对话框已打开')
  } catch (error) {
    console.error('❌ 打开编辑对话框失败:', error)
    ElMessage.error('打开编辑对话框失败')
  }
}

const showAgentCommissionDialog = (agent) => {
  try {
    console.log('💰 显示佣金管理对话框:', agent)

    if (!agent) {
      console.error('❌ 代理商数据为空')
      ElMessage.error('无法查看佣金：代理商数据为空')
      return
    }

    selectedAgent.value = agent
    showCommissionDialog.value = true

    // 加载该代理商的佣金数据
    loadAgentCommissionData(agent.id)

    console.log('✅ 佣金管理对话框已打开')
  } catch (error) {
    console.error('❌ 打开佣金管理对话框失败:', error)
    ElMessage.error('打开佣金管理对话框失败')
  }
}

const showAgentPerformanceDialog = (agent) => {
  try {
    console.log('📊 显示业绩分析对话框:', agent)

    if (!agent) {
      console.error('❌ 代理商数据为空')
      ElMessage.error('无法查看业绩：代理商数据为空')
      return
    }

    selectedAgent.value = agent
    showPerformanceDialog.value = true

    // 加载该代理商的业绩数据
    loadAgentPerformanceData(agent.id)

    console.log('✅ 业绩分析对话框已打开')
  } catch (error) {
    console.error('❌ 打开业绩分析对话框失败:', error)
    ElMessage.error('打开业绩分析对话框失败')
  }
}

// 加载代理商佣金数据
const loadAgentCommissionData = async (agentId) => {
  try {
    console.log(`加载代理商佣金数据: ${agentId}`)

    // 调用真实API获取佣金数据
    const response = await agentApi.getCommissions(agentId, {
      limit: 10
    })

    if (response.success) {
      // 更新模拟佣金记录为真实数据
      mockCommissionRecords.value = response.data.data || []
      console.log('佣金数据加载完成:', response.data)
    } else {
      throw new Error(response.message || '获取佣金数据失败')
    }
  } catch (error) {
    console.error('加载佣金数据失败:', error)
    ElMessage.error('加载佣金数据失败: ' + (error.message || '网络错误'))
    
    // 使用模拟数据作为后备
    mockCommissionRecords.value = [
      {
        id: 1,
        order_no: 'ORD202401001',
        amount: 150.00,
        rate: 15,
        status: 'settled',
        created_at: '2024-01-15 10:30:00'
      },
      {
        id: 2,
        order_no: 'ORD202401002',
        amount: 80.00,
        rate: 8,
        status: 'pending',
        created_at: '2024-01-16 14:20:00'
      }
    ]
  }
}

// 加载代理商业绩数据
const loadAgentPerformanceData = async (agentId) => {
  try {
    console.log(`加载代理商业绩数据: ${agentId}`)

    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 500))

    // 模拟业绩数据
    const mockPerformanceData = {
      total_orders: 156,
      total_sales: 45680.00,
      conversion_rate: 12.8,
      customer_count: 89,
      team_performance: 78.5,
      ranking: 5,
      recent_orders: [
        {
          id: 1,
          order_no: 'ORD202401001',
          amount: 299.00,
          customer_name: '张三',
          status: 'completed',
          created_at: '2024-01-15 10:30:00'
        },
        {
          id: 2,
          order_no: 'ORD202401002',
          amount: 199.00,
          customer_name: '李四',
          status: 'processing',
          created_at: '2024-01-16 14:20:00'
        }
      ]
    }

    // 这里可以将数据存储到响应式变量中，供对话框使用
    console.log('业绩数据加载完成:', mockPerformanceData)

  } catch (error) {
    console.error('加载业绩数据失败:', error)
    ElMessage.error('加载业绩数据失败')
  }
}

// 编辑提交处理
const handleEditSubmit = async () => {
  try {
    console.log('📝 提交编辑表单')

    if (!editFormRef.value) {
      console.error('❌ 编辑表单引用不存在')
      ElMessage.error('表单引用错误')
      return
    }

    await editFormRef.value.validate()
    editLoading.value = true

    console.log('⏳ 正在保存代理商信息...')

    // 调用API更新代理商信息
    const response = await agentApi.update(selectedAgent.value.id, {
      agent_name: editForm.value.agent_name,
      phone: editForm.value.phone,
      email: editForm.value.email,
      agent_level: editForm.value.agent_level,
      remark: editForm.value.remark
    })

    if (response.success) {
      ElMessage.success(`代理商 ${editForm.value.agent_name} 信息更新成功`)
      showEditDialog.value = false
      console.log('✅ 代理商信息更新成功')
      // 重新加载数据
      await loadHierarchyData()
    } else {
      throw new Error(response.message || '更新失败')
    }

  } catch (error) {
    if (error !== false) { // 不是表单验证错误
      console.error('❌ 更新代理商信息失败:', error)
      ElMessage.error('更新失败: ' + (error.message || '网络错误'))
    }
  } finally {
    editLoading.value = false
  }
}

// 转移提交处理
const handleTransferSubmit = async () => {
  try {
    console.log('🔄 提交转移表单')

    if (!transferFormRef.value) {
      console.error('❌ 转移表单引用不存在')
      ElMessage.error('表单引用错误')
      return
    }

    await transferFormRef.value.validate()
    transferLoading.value = true

    console.log('⏳ 正在转移代理商...')

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    const targetText = transferForm.value.new_parent_id ? '指定上级' : '总部直属'
    ElMessage.success(`代理商 ${selectedAgent.value.agent_name} 已成功转移到 ${targetText}`)
    showTransferDialog.value = false

    console.log('✅ 代理商转移成功')

    // 重新加载数据
    await loadHierarchyData()

  } catch (error) {
    if (error !== false) { // 不是表单验证错误
      console.error('❌ 转移代理商失败:', error)
      ElMessage.error('转移失败，请重试')
    }
  } finally {
    transferLoading.value = false
  }
}

const showTransferAgentDialog = (agent) => {
  try {
    console.log('🔄 显示转移代理对话框:', agent)

    if (!agent) {
      console.error('❌ 代理商数据为空')
      ElMessage.error('无法转移：代理商数据为空')
      return
    }

    selectedAgent.value = agent
    transferForm.value = {
      agent_id: agent.id,
      new_parent_id: null,
      reason: ''
    }
    showTransferDialog.value = true

    console.log('✅ 转移代理对话框已打开')
  } catch (error) {
    console.error('❌ 打开转移代理对话框失败:', error)
    ElMessage.error('打开转移代理对话框失败')
  }
}

const handleNodeCommand = async (command) => {
  try {
    console.log('🔧 处理节点命令:', command)

    // 确保命令对象存在
    if (!command || typeof command !== 'object') {
      console.error('❌ 无效的命令对象:', command)
      ElMessage.error('操作失败：无效的命令')
      return
    }

    const { action, data } = command

    // 确保action和data都存在
    if (!action) {
      console.error('❌ 缺少操作类型:', command)
      ElMessage.error('操作失败：缺少操作类型')
      return
    }

    if (!data) {
      console.error('❌ 缺少代理商数据:', command)
      ElMessage.error('操作失败：缺少代理商数据')
      return
    }

    console.log(`🎯 执行操作: ${action}, 代理商: ${data.agent_name || data.id}`)

    switch (action) {
      case 'edit':
        console.log('📝 打开编辑对话框')
        showEditAgentDialog(data)
        break
      case 'commission':
        console.log('💰 打开佣金管理对话框')
        showAgentCommissionDialog(data)
        break
      case 'performance':
        console.log('📊 打开业绩分析对话框')
        showAgentPerformanceDialog(data)
        break
      case 'transfer':
        console.log('🔄 打开转移代理对话框')
        showTransferAgentDialog(data)
        break
      case 'disable':
        console.log('⏸️ 停用代理商')
        await handleToggleStatus(data, 'inactive')
        break
      case 'enable':
        console.log('▶️ 启用代理商')
        await handleToggleStatus(data, 'active')
        break
      default:
        console.warn('⚠️ 未知的操作类型:', action)
        ElMessage.warning(`未知的操作类型: ${action}`)
        break
    }

    console.log('✅ 命令处理完成')

  } catch (error) {
    console.error('❌ 处理命令时发生错误:', error)
    ElMessage.error('操作失败，请重试')
  }
}

const handleToggleStatus = async (agent, status) => {
  try {
    console.log(`🔄 切换代理商状态: ${agent.agent_name} -> ${status}`)

    if (!agent) {
      console.error('❌ 代理商数据为空')
      ElMessage.error('操作失败：代理商数据为空')
      return
    }

    const action = status === 'active' ? '启用' : '停用'

    await ElMessageBox.confirm(
      `确定要${action}代理商 "${agent.agent_name}" 吗？`,
      `确认${action}`,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    console.log(`⏳ 正在${action}代理商...`)

    // 调用API更新状态
    const response = await agentApi.updateStatus(agent.id, status)

    if (response.success) {
      ElMessage.success(`代理商 ${agent.agent_name} ${action}成功`)
      console.log(`✅ 代理商状态更新成功: ${agent.agent_name} -> ${status}`)
      // 重新加载层级数据
      await loadHierarchyData()
    } else {
      throw new Error(response.message || `${action}失败`)
    }

  } catch (error) {
    if (error === 'cancel') {
      console.log('ℹ️ 用户取消了操作')
      ElMessage.info('已取消操作')
    } else {
      console.error('❌ 切换状态失败:', error)
      ElMessage.error('操作失败: ' + (error.message || '网络错误'))
    }
  }
}

const getLevelTagType = (level) => {
  const typeMap = {
    junior: 'info',
    intermediate: 'warning',
    senior: 'success',
    gold: 'danger'
  }
  return typeMap[level] || 'info'
}

const getLevelText = (level) => {
  const textMap = {
    junior: '初级代理',
    intermediate: '中级代理',
    senior: '高级代理',
    gold: '金牌代理'
  }
  return textMap[level] || '未知等级'
}

const formatDate = (dateString) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const loadHierarchyData = async () => {
  loading.value = true

  try {
    console.log('加载团队层级数据...')
    
    // 调用真实API获取层级数据
    const response = await agentApi.getHierarchy({
      page: currentPage.value,
      limit: pageSize.value
    })

    if (response.success) {
      hierarchyData.value = response.data.data || []
      
      // 计算统计数据
      const calculateStats = (data) => {
        let totalAgents = 0
        let directChildren = 0
        let maxLevel = 0
        let totalCommission = 0

        const traverse = (nodes, level = 1) => {
          nodes.forEach(node => {
            totalAgents++
            totalCommission += node.total_commission || 0
            maxLevel = Math.max(maxLevel, level)

            if (level === 1) {
              directChildren += node.children_count || 0
            }

            if (node.children && node.children.length > 0) {
              traverse(node.children, level + 1)
            }
          })
        }

        traverse(data)

        return { totalAgents, directChildren, maxLevel, totalCommission }
      }

      const stats = calculateStats(hierarchyData.value)
      Object.assign(hierarchyStats, stats)
      
      console.log('团队层级数据加载完成')
    } else {
      throw new Error(response.message || '获取数据失败')
    }

  } catch (error) {
    console.error('加载层级数据失败:', error)
    ElMessage.error('加载数据失败: ' + (error.message || '网络错误'))
    
    // 使用模拟数据作为后备
    hierarchyData.value = [
      {
        id: 1,
        agent_name: '张三',
        agent_code: 'A001',
        agent_level: 'gold',
        phone: '13800138001',
        email: '<EMAIL>',
        avatar: '',
        status: 'active',
        children_count: 2,
        total_commission: 15000,
        month_commission: 3000,
        total_orders: 150,
        month_orders: 25,
        team_count: 8,
        created_at: '2024-01-01',
        children: [
          {
            id: 2,
            agent_name: '李四',
            agent_code: 'A002',
            agent_level: 'senior',
            phone: '13800138002',
            email: '<EMAIL>',
            avatar: '',
            status: 'active',
            children_count: 1,
            total_commission: 8000,
            month_commission: 1500,
            total_orders: 80,
            month_orders: 12,
            team_count: 3,
            created_at: '2024-01-15',
            children: [
              {
                id: 3,
                agent_name: '王五',
                agent_code: 'A003',
                agent_level: 'intermediate',
                phone: '13800138003',
                email: '<EMAIL>',
                avatar: '',
                status: 'active',
                children_count: 0,
                total_commission: 3000,
                month_commission: 800,
                total_orders: 30,
                month_orders: 8,
                team_count: 1,
                created_at: '2024-02-01',
                children: []
              }
            ]
          }
        ]
      }
    ]
    
    // 重新计算统计数据
    const calculateStats = (data) => {
      let totalAgents = 0
      let directChildren = 0
      let maxLevel = 0
      let totalCommission = 0

      const traverse = (nodes, level = 1) => {
        nodes.forEach(node => {
          totalAgents++
          totalCommission += node.total_commission || 0
          maxLevel = Math.max(maxLevel, level)

          if (level === 1) {
            directChildren += node.children_count || 0
          }

          if (node.children && node.children.length > 0) {
            traverse(node.children, level + 1)
          }
        })
      }

      traverse(data)

      return { totalAgents, directChildren, maxLevel, totalCommission }
    }

    const stats = calculateStats(hierarchyData.value)
    Object.assign(hierarchyStats, stats)
  } finally {
    loading.value = false
  }
}

// 页面初始化
onMounted(() => {
  loadHierarchyData()
  loadStats()
})
</script>

<style lang="scss" scoped>
.modern-agent-hierarchy {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);

  // 页面头部样式
  .page-header {
    background: white;
    border-bottom: 1px solid #e4e7ed;
    padding: 24px 0;
    margin-bottom: 24px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    
    .header-content {
      max-width: 1400px;
      margin: 0 auto;
      padding: 0 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .header-left {
        display: flex;
        align-items: center;
        gap: 16px;
        
        .header-icon {
          width: 48px;
          height: 48px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
        }
        
        .header-text {
          h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
            color: #303133;
            line-height: 1.2;
          }
          
          p {
            margin: 4px 0 0 0;
            font-size: 14px;
            color: #909399;
            line-height: 1.4;
          }
        }
      }
      
      .header-actions {
        display: flex;
        gap: 12px;
        
        .action-btn {
          height: 40px;
          padding: 0 20px;
          border-radius: 8px;
          font-weight: 500;
          transition: all 0.3s ease;
          
          &.secondary {
            background: #f5f7fa;
            border-color: #dcdfe6;
            color: #606266;
            
            &:hover {
              background: #ecf5ff;
              border-color: #409eff;
              color: #409eff;
            }
          }
          
          &.primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            
            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            }
          }
        }
      }
    }
  }

  // 统计卡片区域
  .stats-section {
    max-width: 1400px;
    margin: 0 auto 24px;
    padding: 0 24px;
    
    .stats-container {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 20px;
      
      .stat-card {
        background: white;
        border-radius: 16px;
        padding: 24px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        display: flex;
        align-items: center;
        gap: 16px;
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }
        
        .stat-icon {
          width: 56px;
          height: 56px;
          border-radius: 14px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          flex-shrink: 0;
        }
        
        .stat-content {
          flex: 1;
          
          .stat-value {
            font-size: 28px;
            font-weight: 700;
            color: #303133;
            line-height: 1.2;
            margin-bottom: 4px;
          }
          
          .stat-label {
            font-size: 14px;
            color: #909399;
            font-weight: 500;
          }
        }
        
        .stat-trend {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 12px;
          font-weight: 600;
          
          &.up {
            color: #67c23a;
          }
          
          &.down {
            color: #f56c6c;
          }
        }
      }
    }
  }

  // 表格卡片样式
  .table-card {
    max-width: 1400px;
    margin: 0 auto 40px;
    padding: 0 24px;
    border-radius: 16px;
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    
    :deep(.el-card__header) {
      background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
      border-bottom: 1px solid #e4e7ed;
      padding: 20px 24px;
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .header-left {
          display: flex;
          align-items: center;
          gap: 12px;
          
          h3 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #303133;
          }
          
          .el-tag {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            border: none;
          }
        }
      }
    }
    
    :deep(.el-card__body) {
      padding: 24px;
    }
  }

  // 树形视图
  .tree-view {
    padding: 20px 0;
  }

  .modern-tree {
    --el-tree-node-hover-bg-color: #f8fafc;
    --el-tree-node-content-height: auto;
  }

  .tree-node {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 16px 20px;
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  margin-bottom: 8px;
  background: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

  .tree-node:hover {
    background: #f8fafc;
    border-color: #e0e7ff;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    transform: translateY(-2px);
  }

  .node-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
  }

  .node-avatar {
    border: 2px solid #e5e7eb;
  }

  .node-details {
    flex: 1;
  }

  .node-name {
    font-size: 14px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 4px;
  }

  .node-meta {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .node-code {
    font-size: 12px;
    color: #6b7280;
    background: #f3f4f6;
    padding: 2px 6px;
    border-radius: 4px;
  }

  .node-stats {
    display: flex;
    gap: 16px;
    margin: 0 16px;
  }

  .stat-item {
    text-align: center;
  }

  .stat-label {
    font-size: 12px;
    color: #6b7280;
    margin-right: 4px;
  }

  .stat-value {
    font-size: 12px;
    font-weight: 600;
    color: #1f2937;
  }

      .node-actions {
      display: flex;
      gap: 8px;
      
      .el-button {
        height: 32px;
        padding: 8px 12px;
        border-radius: 6px;
        font-size: 12px;
        
        &.is-link {
          padding: 4px 8px;
          
          &:hover {
            background-color: rgba(64, 158, 255, 0.1);
          }
        }
      }
      
      .el-dropdown {
        .el-button {
          border: 1px solid #e4e7ed;
          background: #fff;
          
          &:hover {
            border-color: #409eff;
            color: #409eff;
          }
        }
      }
    }

    /* 表格视图 */
    .table-view {
      padding: 20px 0;
    }

    .hierarchy-table {
      --el-table-border-color: #e5e7eb;
      --el-table-bg-color: white;
    }

    .agent-info {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .agent-details {
      flex: 1;
    }

    .agent-name {
      font-size: 14px;
      font-weight: 600;
      color: #1f2937;
      margin-bottom: 2px;
    }

    .agent-code {
      font-size: 12px;
      color: #6b7280;
    }

    .level-path {
      display: flex;
      align-items: center;
      gap: 4px;
      flex-wrap: wrap;
    }

    .level-item {
      font-size: 12px;
      color: #6b7280;
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .children-badge {
      display: inline-flex;
    }

    .commission-amount {
      font-weight: 600;
      color: #059669;
    }

    /* 对话框样式 */
    .add-agent-dialog .el-form-item {
      margin-bottom: 20px;
    }

    .detail-dialog {
      .agent-detail {
        .detail-header {
          display: flex;
          align-items: center;
          gap: 20px;
          padding: 20px 0;
          border-bottom: 1px solid #e5e7eb;
          margin-bottom: 20px;
        }

        .agent-info h3 {
          margin: 0 0 8px 0;
          font-size: 20px;
          font-weight: 600;
          color: #1f2937;
        }

        .agent-code {
          margin: 0 0 12px 0;
          font-size: 14px;
          color: #6b7280;
        }
      }
    }

    .detail-tabs {
      .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 16px;
        padding: 20px 0;
      }

      .info-item {
        display: flex;
        flex-direction: column;
        gap: 4px;

        label {
          font-size: 12px;
          color: #6b7280;
          font-weight: 500;
        }

        span {
          font-size: 14px;
          color: #1f2937;
          font-weight: 500;
        }
      }
    }

    .performance-stats {
      padding: 20px 0;

      .stat-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 20px;

        .stat-item {
          text-align: center;
          padding: 20px;
          background: #f8fafc;
          border-radius: 8px;

          .stat-value {
            font-size: 24px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
          }

          .stat-label {
            font-size: 14px;
            color: #6b7280;
          }
        }
      }
    }

    .team-structure {
      padding: 40px 20px;
      text-align: center;
      color: #6b7280;
    }

    /* 响应式设计 */
    @media (max-width: 1200px) {
      .stats-container {
        grid-template-columns: repeat(2, 1fr);
      }
      
      .page-header .header-content {
        padding: 0 16px;
      }
      
      .table-card {
        padding: 0 16px;
      }
    }
    
    @media (max-width: 768px) {
      .modern-agent-hierarchy {
        background: #f8f9fa;
      }
      
      .page-header .header-content {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
        padding: 0 12px;
        
        .header-actions {
          flex-wrap: wrap;
          gap: 8px;
          
          .action-btn {
            flex: 1;
            min-width: 120px;
            height: 36px;
            font-size: 13px;
          }
        }
      }

      .stats-container {
        grid-template-columns: 1fr;
        gap: 12px;
        padding: 0 12px;
        
        .stat-card {
          padding: 16px;
          
          .stat-icon {
            width: 48px;
            height: 48px;
          }
          
          .stat-content .stat-value {
            font-size: 24px;
          }
        }
      }

      .tree-node {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
        padding: 12px 16px;
        margin-bottom: 12px;
        
        .node-info {
          gap: 8px;
        }
        
        .node-avatar {
          width: 28px;
          height: 28px;
        }
        
        .node-stats {
          flex-direction: row;
          justify-content: space-around;
          margin: 0;
          gap: 8px;
          
          .stat-item {
            text-align: center;
            min-width: 60px;
          }
        }

        .node-actions {
          justify-content: center;
          gap: 6px;
          
          .el-button {
            height: 28px;
            padding: 6px 10px;
            font-size: 11px;
          }
        }
      }
      
      .table-card {
        margin: 0 12px 40px;
        border-radius: 12px;
        
        :deep(.el-card__body) {
          padding: 16px;
        }
      }
      
      .pagination {
        padding: 0 12px;
        
        :deep(.el-pagination) {
          justify-content: center;
          
          .el-pagination__sizes,
          .el-pagination__total {
            display: none;
          }
        }
      }
    }
    
    @media (max-width: 480px) {
      .page-header .header-content .header-actions {
        .action-btn {
          min-width: 100px;
          height: 32px;
          font-size: 12px;
          padding: 0 12px;
        }
      }
      
      .stats-container .stat-card {
        padding: 12px;
        
        .stat-icon {
          width: 40px;
          height: 40px;
        }
        
        .stat-content {
          .stat-value {
            font-size: 20px;
          }
          
          .stat-label {
            font-size: 12px;
          }
        }
        
        .stat-trend {
          font-size: 10px;
        }
      }
      
      .tree-node {
        padding: 10px 12px;
        
        .node-name {
          font-size: 13px;
        }
        
        .node-code {
          font-size: 10px;
        }
        
        .stat-label,
        .stat-value {
          font-size: 10px;
        }
        
        .node-actions .el-button {
          height: 24px;
          padding: 4px 8px;
          font-size: 10px;
        }
      }
    }
  }

  /* 对话框样式 */
  .edit-agent-dialog,
  .commission-dialog,
  .performance-dialog,
  .transfer-dialog {
    .el-dialog__body {
      padding: 20px 30px;
    }
  }

  .commission-content,
  .performance-content {
    .commission-header,
    .performance-header {
      text-align: center;
      margin-bottom: 20px;

      h3 {
        margin: 0;
        color: #409eff;
        font-size: 18px;
      }
    }

    .commission-stats,
    .performance-stats {
      margin-bottom: 30px;

      .stat-card {
        text-align: center;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #e9ecef;

        .stat-value {
          font-size: 24px;
          font-weight: bold;
          color: #409eff;
          margin-bottom: 8px;
        }

        .stat-label {
          font-size: 14px;
          color: #666;
        }
      }
    }

    .commission-records,
    .performance-records {
      h4 {
        margin: 0 0 15px 0;
        color: #333;
        font-size: 16px;
      }

      .amount {
        color: #67c23a;
        font-weight: bold;
      }
    }
  }

  .dialog-footer {
    text-align: center;
    padding: 10px 0;

    .el-button {
      margin: 0 8px;
      min-width: 80px;
    }
  }
</style>