<template>
  <div class="modern-agent-dashboard">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <div class="header-icon">
            <el-icon size="24"><UserFilled /></el-icon>
          </div>
          <div class="header-text">
            <h1>代理商工作台</h1>
            <p>管理您的推广业务，跟踪团队业绩，提升佣金收入</p>
          </div>
        </div>
        <div class="header-actions">
          <div class="agent-badges">
            <el-tag type="primary" class="agent-tag">{{ agentInfo.agent_level_text || '平台代理' }}</el-tag>
            <el-tag type="success" class="agent-tag">{{ agentInfo.agent_type_text || '个人代理' }}</el-tag>
            <span class="agent-code">编码: {{ agentInfo.agent_code || 'AG001' }}</span>
          </div>
          <div class="action-buttons">
            <el-button @click="showHelpDialog = true" class="action-btn secondary">
              <el-icon><QuestionFilled /></el-icon>
              功能说明
            </el-button>
            <el-button type="primary" @click="refreshAllData" class="action-btn primary">
              <el-icon><Refresh /></el-icon>
              刷新数据
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 统计卡片区域 -->
    <div class="stats-section">
      <div class="stats-container" v-loading="statsLoading">
        <div class="stat-card" v-for="stat in agentStatCards" :key="stat.key">
          <div class="stat-icon" :style="{ background: stat.color }">
            <el-icon size="20">
              <component :is="stat.icon" />
            </el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stat.value }}</div>
            <div class="stat-label">{{ stat.label }}</div>
          </div>
          <div class="stat-trend" :class="stat.trend">
            <el-icon size="14">
              <component :is="stat.trendIcon" />
            </el-icon>
            <span>{{ stat.change }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 快捷操作 -->
    <el-card class="quick-actions-card">
      <template #header>
        <span>快捷操作</span>
      </template>
      <el-row :gutter="15">
        <el-col :span="4">
          <div class="action-item" @click="goToPromotionTools">
            <el-icon class="action-icon"><Share /></el-icon>
            <span>推广工具</span>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="action-item" @click="goToTeamManagement">
            <el-icon class="action-icon"><Connection /></el-icon>
            <span>团队管理</span>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="action-item" @click="goToCommissionCenter">
            <el-icon class="action-icon"><Money /></el-icon>
            <span>佣金中心</span>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="action-item" @click="goToTrainingCenter">
            <el-icon class="action-icon"><Reading /></el-icon>
            <span>培训中心</span>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="action-item" @click="goToPerformanceAnalysis">
            <el-icon class="action-icon"><TrendCharts /></el-icon>
            <span>绩效分析</span>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="action-item" @click="goToSettings">
            <el-icon class="action-icon"><Setting /></el-icon>
            <span>账户设置</span>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 数据图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>佣金收入趋势</span>
              <el-radio-group v-model="commissionPeriod" size="small" @change="loadCommissionTrend">
                <el-radio-button label="week">近7天</el-radio-button>
                <el-radio-button label="month">近30天</el-radio-button>
                <el-radio-button label="quarter">近3个月</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div style="height: 300px; display: flex; align-items: center; justify-content: center; background: #f5f7fa; border-radius: 8px;">
            <div class="chart-placeholder">
              <el-icon size="48" color="#ccc"><TrendCharts /></el-icon>
              <p style="color: #999; margin-top: 12px;">佣金趋势图表</p>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>用户来源分析</span>
          </template>
          <div style="height: 300px; display: flex; align-items: center; justify-content: center; background: #f5f7fa; border-radius: 8px;">
            <div class="chart-placeholder">
              <el-icon size="48" color="#ccc"><PieChart /></el-icon>
              <p style="color: #999; margin-top: 12px;">用户来源分析图表</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 团队概览和最新动态 -->
    <el-row :gutter="20" class="info-row">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>我的团队</span>
              <el-button size="small" @click="goToTeamManagement">查看全部</el-button>
            </div>
          </template>
          <div class="team-overview">
            <div class="team-stats">
              <div class="team-stat-item">
                <span class="label">直属下级:</span>
                <span class="value">{{ teamStats.direct_children || 0 }}</span>
              </div>
              <div class="team-stat-item">
                <span class="label">团队总人数:</span>
                <span class="value">{{ teamStats.total_children || 0 }}</span>
              </div>
              <div class="team-stat-item">
                <span class="label">活跃成员:</span>
                <span class="value">{{ teamStats.active_members || 0 }}</span>
              </div>
            </div>
            <div class="recent-members">
              <h4>最新加入成员</h4>
              <div v-for="member in recentMembers" :key="member.id" class="member-item">
                <el-avatar :size="32" :src="member.avatar">
                  {{ member.name?.charAt(0) }}
                </el-avatar>
                <div class="member-info">
                  <div class="member-name">{{ member.name }}</div>
                  <div class="member-time">{{ formatDate(member.created_at) }}</div>
                </div>
                <el-tag size="small" :type="getMemberTypeColor(member.agent_type)">
                  {{ member.agent_type_text }}
                </el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>最新动态</span>
              <el-button size="small" @click="refreshActivities">刷新</el-button>
            </div>
          </template>
          <div class="activities-list">
            <div v-for="activity in recentActivities" :key="activity.id" class="activity-item">
              <div class="activity-icon">
                <el-icon :color="getActivityColor(activity.type)">
                  <component :is="getActivityIcon(activity.type)" />
                </el-icon>
              </div>
              <div class="activity-content">
                <div class="activity-title">{{ activity.title }}</div>
                <div class="activity-desc">{{ activity.description }}</div>
                <div class="activity-time">{{ formatDate(activity.created_at) }}</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 推广链接快速生成 -->
    <el-card class="promotion-card">
      <template #header>
        <span>推广链接快速生成</span>
      </template>
      <el-row :gutter="20">
        <el-col :span="16">
          <el-input
            v-model="promotionLink"
            placeholder="您的专属推广链接"
            readonly
          >
            <template #prepend>推广链接</template>
            <template #append>
              <el-button @click="copyPromotionLink">
                <el-icon><DocumentCopy /></el-icon>
                复制
              </el-button>
            </template>
          </el-input>
        </el-col>
        <el-col :span="8">
          <el-button type="primary" @click="generateQRCode">
            <el-icon><Setting /></el-icon>
            生成二维码
          </el-button>
          <el-button @click="goToPromotionTools">
            <el-icon><Setting /></el-icon>
            更多工具
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 二维码对话框 -->
    <el-dialog v-model="qrCodeDialogVisible" title="推广二维码" width="400px">
      <div class="qr-code-container">
        <canvas ref="qrCodeRef" class="qr-code" width="200" height="200"></canvas>
        <div class="qr-code-tips">
          <p>扫描二维码或分享链接进行推广</p>
          <el-button type="primary" @click="downloadQRCode">下载二维码</el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 功能说明对话框 -->
    <el-dialog
      v-model="showHelpDialog"
      title="代理商工作台功能说明"
      width="1000px"
      class="help-dialog"
    >
      <div class="help-content">
        <div class="help-section">
          <h3>🎯 功能概述</h3>
          <p>代理商工作台是您管理推广业务的核心平台，提供全面的数据统计、团队管理、佣金跟踪等功能，帮助您高效开展推广业务，最大化收益。</p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  Share, Connection, Money, Reading, TrendCharts, Setting, 
  User, Coin, DocumentCopy, QuestionFilled, UserFilled, Refresh, ArrowUp, PieChart
} from '@element-plus/icons-vue'
import { agentApi } from '@/api/agent'
import QRCode from 'qrcode'

// 路由实例
const router = useRouter()

// 响应式数据
const loading = ref(false)
const statsLoading = ref(true)
const commissionPeriod = ref('month')
const qrCodeDialogVisible = ref(false)
const showHelpDialog = ref(false)
const qrCodeRef = ref()

const agentInfo = ref({})
const stats = ref({})
const teamStats = ref({
  direct_children: 12,
  total_children: 45,
  active_members: 38
})
const recentMembers = ref([
  {
    id: 1,
    name: '张三',
    avatar: '',
    agent_type: 'individual',
    agent_type_text: '个人代理',
    created_at: new Date()
  },
  {
    id: 2,
    name: '李四',
    avatar: '',
    agent_type: 'enterprise',
    agent_type_text: '企业代理',
    created_at: new Date(Date.now() - 86400000)
  }
])
const recentActivities = ref([
  {
    id: 1,
    type: 'commission',
    title: '佣金到账',
    description: '您获得了 ¥150.00 的推广佣金',
    created_at: new Date()
  },
  {
    id: 2,
    type: 'user',
    title: '新用户注册',
    description: '通过您的推广链接，新增用户"王五"',
    created_at: new Date(Date.now() - 3600000)
  }
])
const promotionLink = ref('')

// 代理商统计卡片数据
const agentStatCards = ref([
  {
    key: 'total_users',
    label: '推广用户数',
    value: '234',
    icon: 'User',
    color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '+15.2%'
  },
  {
    key: 'total_commission',
    label: '总佣金收入',
    value: '¥45,670',
    icon: 'Money',
    color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '+28.5%'
  },
  {
    key: 'monthly_commission',
    label: '本月佣金',
    value: '¥8,950',
    icon: 'Coin',
    color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '+18.2%'
  },
  {
    key: 'child_agents',
    label: '下级代理商',
    value: '12',
    icon: 'Connection',
    color: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
    trend: 'up',
    trendIcon: 'ArrowUp',
    change: '+5'
  }
])

// 方法
const loadAgentInfo = async () => {
  try {
    // 模拟API调用
    agentInfo.value = {
      agent_level_text: '平台代理',
      agent_type_text: '个人代理',
      agent_code: 'AG001'
    }
    generatePromotionLink()
  } catch (error) {
    ElMessage.error('加载代理商信息失败')
  }
}

const loadCommissionTrend = async () => {
  // 模拟数据加载
  console.log('加载佣金趋势:', commissionPeriod.value)
}

const refreshActivities = () => {
  ElMessage.success('动态已刷新')
}

const generatePromotionLink = () => {
  if (agentInfo.value.agent_code) {
    promotionLink.value = `${window.location.origin}/register?agent=${agentInfo.value.agent_code}`
  }
}

const copyPromotionLink = async () => {
  try {
    await navigator.clipboard.writeText(promotionLink.value)
    ElMessage.success('推广链接已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败，请手动复制')
  }
}

const generateQRCode = async () => {
  try {
    qrCodeDialogVisible.value = true
    await nextTick()

    // 确保canvas元素存在
    if (qrCodeRef.value) {
      const canvas = qrCodeRef.value
      const ctx = canvas.getContext('2d')
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      await QRCode.toCanvas(canvas, promotionLink.value, {
        width: 200,
        height: 200,
        margin: 2,
        errorCorrectionLevel: 'M'
      })

      ElMessage.success('二维码生成成功')
    }
  } catch (error) {
    console.error('生成二维码失败:', error)
    ElMessage.error('生成二维码失败')
  }
}

const downloadQRCode = () => {
  const canvas = qrCodeRef.value.querySelector('canvas')
  if (canvas) {
    const link = document.createElement('a')
    link.download = `推广二维码-${agentInfo.value.agent_code}.png`
    link.href = canvas.toDataURL()
    link.click()
  }
}

// 导航方法
const goToPromotionTools = () => {
  router.push('/admin/promotion/links')
}

const goToTeamManagement = () => {
  router.push('/admin/partners/hierarchy')
}

const goToCommissionCenter = () => {
  router.push('/admin/partners/commission')
}

const goToTrainingCenter = () => {
  router.push('/admin/partners/list?type=agent')
}

const goToPerformanceAnalysis = () => {
  router.push('/admin/partners/list?type=agent')
}

const goToSettings = () => {
  router.push('/admin/system/settings')
}

// 工具方法
const formatDate = (date) => {
  return new Date(date).toLocaleString('zh-CN')
}

const getMemberTypeColor = (type) => {
  const colors = {
    'individual': 'primary',
    'enterprise': 'success',
    'channel': 'warning'
  }
  return colors[type] || 'info'
}

const getActivityColor = (type) => {
  const colors = {
    'commission': '#67C23A',
    'user': '#409EFF',
    'team': '#E6A23C'
  }
  return colors[type] || '#909399'
}

const getActivityIcon = (type) => {
  const icons = {
    'commission': 'Money',
    'user': 'User',
    'team': 'Connection'
  }
  return icons[type] || 'InfoFilled'
}

// 刷新所有数据
const refreshAllData = async () => {
  try {
    statsLoading.value = true
    await loadAgentInfo()
    ElMessage.success('数据刷新成功')
  } catch (error) {
    ElMessage.error('数据刷新失败')
  } finally {
    statsLoading.value = false
  }
}

// 生命周期
onMounted(() => {
  loadAgentInfo()
  statsLoading.value = false
})
</script>

<style lang="scss" scoped>
.modern-agent-dashboard {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  
  .page-header {
    background: white;
    border-bottom: 1px solid #e4e7ed;
    padding: 24px 0;
    margin-bottom: 24px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    
    .header-content {
      max-width: 1400px;
      margin: 0 auto;
      padding: 0 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .header-left {
        display: flex;
        align-items: center;
        gap: 16px;
        
        .header-icon {
          width: 48px;
          height: 48px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
        }
        
        .header-text {
          h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
            color: #303133;
            line-height: 1.2;
          }
          
          p {
            margin: 4px 0 0 0;
            font-size: 14px;
            color: #909399;
            line-height: 1.4;
          }
        }
      }
      
      .header-actions {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        gap: 12px;
        
        .agent-badges {
          display: flex;
          align-items: center;
          gap: 12px;
          
          .agent-tag {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 4px;
          }
          
          .agent-code {
            color: #909399;
            font-size: 13px;
            font-weight: 500;
          }
        }
        
        .action-buttons {
          display: flex;
          gap: 12px;
          
          .action-btn {
            height: 36px;
            padding: 0 16px;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            
            &.secondary {
              background: #f5f7fa;
              border-color: #dcdfe6;
              color: #606266;
              
              &:hover {
                background: #ecf5ff;
                border-color: #409eff;
                color: #409eff;
              }
            }
            
            &.primary {
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              border: none;
              
              &:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
              }
            }
          }
        }
      }
    }
  }

  .stats-section {
    max-width: 1400px;
    margin: 0 auto 24px;
    padding: 0 24px;
    
    .stats-container {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 20px;
      
      .stat-card {
        background: white;
        border-radius: 16px;
        padding: 24px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        display: flex;
        align-items: center;
        gap: 16px;
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }
        
        .stat-icon {
          width: 56px;
          height: 56px;
          border-radius: 14px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          flex-shrink: 0;
        }
        
        .stat-content {
          flex: 1;
          
          .stat-value {
            font-size: 28px;
            font-weight: 700;
            color: #303133;
            line-height: 1.2;
            margin-bottom: 4px;
          }
          
          .stat-label {
            font-size: 14px;
            color: #909399;
            font-weight: 500;
          }
        }
        
        .stat-trend {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 12px;
          font-weight: 600;
          
          &.up {
            color: #67c23a;
          }
          
          &.down {
            color: #f56c6c;
          }
        }
      }
    }
  }

  .quick-actions-card {
    max-width: 1400px;
    margin: 0 auto 24px;
    border-radius: 16px;
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    
    :deep(.el-card__header) {
      background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
      border-bottom: 1px solid #e4e7ed;
      padding: 20px 24px;
      font-weight: 600;
      color: #303133;
    }
    
    :deep(.el-card__body) {
      padding: 24px;
    }
    
    .action-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 24px 16px;
      border-radius: 12px;
      cursor: pointer;
      transition: all 0.3s ease;
      border: 1px solid #e4e7ed;
      background: white;
      
      &:hover {
        background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
        border-color: #667eea;
        transform: translateY(-4px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
      }
      
      .action-icon {
        font-size: 28px;
        color: #667eea;
        margin-bottom: 12px;
        transition: all 0.3s ease;
      }
      
      span {
        font-weight: 500;
        color: #303133;
        font-size: 14px;
      }
    }
  }

  :deep(.el-card) {
    max-width: 1400px;
    margin: 0 auto 24px;
    border-radius: 16px;
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    
    .el-card__header {
      background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
      border-bottom: 1px solid #e4e7ed;
      padding: 20px 24px;
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        span {
          font-weight: 600;
          color: #303133;
          font-size: 16px;
        }
      }
    }
    
    .el-card__body {
      padding: 24px;
    }
  }

  .charts-row {
    max-width: 1400px;
    margin: 0 auto 24px;
    padding: 0 24px;
  }

  .info-row {
    max-width: 1400px;
    margin: 0 auto 24px;
    padding: 0 24px;
  }

  .promotion-card {
    max-width: 1400px;
    margin: 0 auto 24px;
    padding: 0 24px;
  }

  .chart-placeholder {
    text-align: center;
    
    p {
      margin: 0;
      font-size: 14px;
    }
  }

  .team-overview {
    .team-stats {
      display: flex;
      justify-content: space-around;
      margin-bottom: 20px;
      padding: 20px;
      background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
      border-radius: 12px;
      border: 1px solid #e4e7ed;
      
      .team-stat-item {
        text-align: center;
        
        .label {
          display: block;
          color: #909399;
          font-size: 13px;
          margin-bottom: 8px;
          font-weight: 500;
        }
        
        .value {
          display: block;
          color: #303133;
          font-size: 24px;
          font-weight: 700;
        }
      }
    }
    
    .recent-members {
      h4 {
        margin: 0 0 16px 0;
        color: #303133;
        font-size: 16px;
        font-weight: 600;
      }
      
      .member-item {
        display: flex;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #f0f2f5;
        
        &:last-child {
          border-bottom: none;
        }
        
        .member-info {
          flex: 1;
          margin-left: 12px;
          
          .member-name {
            font-weight: 600;
            color: #303133;
            margin-bottom: 4px;
          }
          
          .member-time {
            font-size: 12px;
            color: #909399;
          }
        }
      }
    }
  }

  .activities-list {
    max-height: 350px;
    overflow-y: auto;
    
    .activity-item {
      display: flex;
      align-items: flex-start;
      padding: 16px 0;
      border-bottom: 1px solid #f0f2f5;
      
      &:last-child {
        border-bottom: none;
      }
      
      .activity-icon {
        margin-right: 12px;
        margin-top: 2px;
      }
      
      .activity-content {
        flex: 1;
        
        .activity-title {
          font-weight: 600;
          color: #303133;
          margin-bottom: 6px;
        }
        
        .activity-desc {
          color: #606266;
          font-size: 14px;
          margin-bottom: 6px;
          line-height: 1.4;
        }
        
        .activity-time {
          color: #909399;
          font-size: 12px;
        }
      }
    }
  }

  .qr-code-container {
    text-align: center;
    
    .qr-code {
      margin-bottom: 20px;
      display: flex;
      justify-content: center;
    }
    
    .qr-code-tips {
      p {
        color: #606266;
        margin-bottom: 15px;
        font-size: 14px;
      }
    }
  }
}

.help-dialog :deep(.el-dialog__body) {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

.help-content {
  .help-section {
    margin-bottom: 30px;
    
    h3 {
      color: #303133;
      margin-bottom: 15px;
      font-size: 18px;
      border-bottom: 2px solid #667eea;
      padding-bottom: 8px;
    }
    
    p {
      color: #606266;
      line-height: 1.6;
      margin-bottom: 15px;
    }
  }
}

@media (max-width: 1200px) {
  .modern-agent-dashboard {
    .stats-container {
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
  }
}

@media (max-width: 768px) {
  .modern-agent-dashboard {
    .page-header .header-content {
      flex-direction: column;
      gap: 16px;
      text-align: center;
      
      .header-actions {
        width: 100%;
        align-items: center;
        
        .agent-badges {
          justify-content: center;
          flex-wrap: wrap;
        }
        
        .action-buttons {
          justify-content: center;
        }
      }
    }
    
    .stats-container {
      grid-template-columns: 1fr;
    }
  }
}
</style>