<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\AgentAccount;
use App\Models\User;
use App\Models\Substation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Validator;

/**
 * 代理商管理控制器
 * 提供代理商的完整管理功能
 */
class AgentController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:sanctum');
        $this->middleware('role:admin')->except(['getMy', 'getMyStats', 'updateMy']);
    }

    /**
     * 获取代理商列表
     */
    public function index(Request $request)
    {
        $query = AgentAccount::with(['user:id,username,name,avatar', 'substation:id,name', 'parentAgent:id,agent_name']);

        // 根据用户角色过滤数据
        $user = $request->user();
        if ($user->hasRole('substation')) {
            $query->where('substation_id', $user->substation_id);
        }

        // 搜索条件
        if ($request->filled('keyword')) {
            $query->where(function ($q) use ($request) {
                $q->where('agent_name', 'like', '%' . $request->keyword . '%')
                  ->orWhere('agent_code', 'like', '%' . $request->keyword . '%')
                  ->orWhereHas('user', function ($userQuery) use ($request) {
                      $userQuery->where('username', 'like', '%' . $request->keyword . '%')
                                ->orWhere('name', 'like', '%' . $request->keyword . '%');
                  });
            });
        }

        // 代理商等级筛选
        if ($request->filled('agent_level')) {
            $query->where('agent_level', $request->agent_level);
        }

        // 代理商类型筛选
        if ($request->filled('agent_type')) {
            $query->where('agent_type', $request->agent_type);
        }

        // 状态筛选
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // 分站筛选
        if ($request->filled('substation_id')) {
            $query->where('substation_id', $request->substation_id);
        }

        // 代理商等级筛选
        if ($request->filled('agent_grade')) {
            $query->where('agent_grade', $request->agent_grade);
        }

        // 有效性筛选
        if ($request->filled('validity')) {
            if ($request->validity === 'valid') {
                $query->valid();
            } elseif ($request->validity === 'expired') {
                $query->expired();
            } elseif ($request->validity === 'expiring') {
                $query->expiringSoon();
            }
        }

        $agents = $query->orderBy('created_at', 'desc')
                       ->paginate($request->input('limit', 20));

        return response()->json([
            'success' => true,
            'data' => $agents,
        ]);
    }

    /**
     * 获取代理商统计数据
     */
    public function getStats(Request $request)
    {
        $user = $request->user();
        $cacheKey = 'agent_stats_' . $user->id . '_' . $user->role;
        
        $stats = Cache::remember($cacheKey, 300, function () use ($user) {
            $query = AgentAccount::query();
            
            // 根据用户角色过滤数据
            if ($user->hasRole('substation')) {
                $query->where('substation_id', $user->substation_id);
            }
            
            $totalAgents = $query->count();
            $activeAgents = (clone $query)->where('status', 'active')->count();
            $platformAgents = (clone $query)->where('agent_level', 'platform')->count();
            $substationAgents = (clone $query)->where('agent_level', 'substation')->count();
            $expiredAgents = (clone $query)->expired()->count();
            $expiringSoonAgents = (clone $query)->expiringSoon()->count();
            
            // 佣金统计
            $totalCommission = (clone $query)->sum('total_commission');
            $monthlyCommission = (clone $query)->whereMonth('created_at', now()->month)->sum('total_commission');
            
            return [
                'total_agents' => $totalAgents,
                'active_agents' => $activeAgents,
                'platform_agents' => $platformAgents,
                'substation_agents' => $substationAgents,
                'expired_agents' => $expiredAgents,
                'expiring_soon_agents' => $expiringSoonAgents,
                'total_commission' => $totalCommission,
                'monthly_commission' => $monthlyCommission,
                'total_users' => (clone $query)->sum('total_users'),
                'total_revenue' => (clone $query)->sum('total_revenue'),
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }

    /**
     * 创建代理商
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'agent_level' => 'required|in:platform,substation',
            'agent_type' => 'required|in:individual,enterprise,channel',
            'agent_name' => 'required|string|max:100',
            'agent_grade' => 'required|integer|min:1|max:5',
            'substation_id' => 'nullable|exists:substations,id',
            'parent_agent_id' => 'nullable|exists:agent_accounts,id',
            'commission_rate' => 'required|numeric|min:0|max:100',
            'validity_period' => 'required|in:week,month,quarter,half_year,year,permanent,custom',
            'custom_end_date' => 'nullable|date|after:today',
            'no_commission' => 'boolean',
            'permissions' => 'nullable|array',
            'remark' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first(),
            ], 422);
        }

        try {
            DB::beginTransaction();

            // 检查用户是否已经是代理商
            if (AgentAccount::where('user_id', $request->user_id)->exists()) {
                return response()->json([
                    'success' => false,
                    'message' => '该用户已经是代理商了',
                ], 422);
            }

            // 生成代理商编码
            $agentCode = AgentAccount::generateAgentCode($request->agent_level);

            // 创建代理商账户
            $agent = AgentAccount::create([
                'user_id' => $request->user_id,
                'agent_level' => $request->agent_level,
                'agent_type' => $request->agent_type,
                'agent_name' => $request->agent_name,
                'agent_grade' => $request->agent_grade,
                'agent_code' => $agentCode,
                'substation_id' => $request->substation_id,
                'parent_agent_id' => $request->parent_agent_id,
                'commission_rate' => $request->commission_rate,
                'no_commission' => $request->no_commission ?? false,
                'permissions' => $request->permissions ?? [],
                'status' => 'active',
                'created_by' => $request->user()->id,
                'remark' => $request->remark,
            ]);

            // 设置有效期
            $agent->setValidityPeriod($request->validity_period, $request->custom_end_date);
            $agent->save();

            // 更新用户角色
            User::where('id', $request->user_id)->update(['role' => 'agent']);

            DB::commit();

            return response()->json([
                'success' => true,
                'data' => $agent->load(['user', 'substation', 'parentAgent']),
                'message' => '代理商创建成功',
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => '创建失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 获取代理商详情
     */
    public function show($id)
    {
        $agent = AgentAccount::with([
            'user:id,username,name,email,phone,avatar',
            'substation:id,name,domain',
            'parentAgent:id,agent_name,agent_code',
            'childAgents:id,agent_name,agent_code,status',
            'creator:id,username,name'
        ])->findOrFail($id);

        // 权限检查
        $user = request()->user();
        if ($user->hasRole('substation') && $agent->substation_id !== $user->substation_id) {
            return response()->json([
                'success' => false,
                'message' => '无权限查看此代理商',
            ], 403);
        }

        // 获取统计数据
        $stats = [
            'total_users' => $agent->promotedUsers()->count(),
            'active_users' => $agent->promotedUsers()->where('status', 1)->count(),
            'total_orders' => 0, // 根据业务逻辑计算
            'total_commission' => $agent->total_commission,
            'monthly_commission' => $agent->commissionLogs()->whereMonth('created_at', now()->month)->sum('commission_amount'),
            'child_agents_count' => $agent->childAgents()->count(),
            'remaining_days' => $agent->getRemainingDays(),
        ];

        return response()->json([
            'success' => true,
            'data' => [
                'agent' => $agent,
                'stats' => $stats,
            ],
        ]);
    }

    /**
     * 更新代理商
     */
    public function update(Request $request, $id)
    {
        $agent = AgentAccount::findOrFail($id);

        // 权限检查
        $user = $request->user();
        if ($user->hasRole('substation') && $agent->substation_id !== $user->substation_id) {
            return response()->json([
                'success' => false,
                'message' => '无权限修改此代理商',
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'agent_name' => 'required|string|max:100',
            'agent_type' => 'required|in:individual,enterprise,channel',
            'agent_grade' => 'required|integer|min:1|max:5',
            'commission_rate' => 'required|numeric|min:0|max:100',
            'no_commission' => 'boolean',
            'permissions' => 'nullable|array',
            'remark' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first(),
            ], 422);
        }

        $agent->update($request->only([
            'agent_name',
            'agent_type', 
            'agent_grade',
            'commission_rate',
            'no_commission',
            'permissions',
            'remark'
        ]));

        return response()->json([
            'success' => true,
            'data' => $agent->load(['user', 'substation', 'parentAgent']),
            'message' => '代理商更新成功',
        ]);
    }

    /**
     * 删除代理商
     */
    public function destroy($id)
    {
        $agent = AgentAccount::findOrFail($id);

        // 权限检查
        $user = request()->user();
        if ($user->hasRole('substation') && $agent->substation_id !== $user->substation_id) {
            return response()->json([
                'success' => false,
                'message' => '无权限删除此代理商',
            ], 403);
        }

        // 检查是否有下级代理商
        if ($agent->childAgents()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => '该代理商有下级代理商，无法删除',
            ], 422);
        }

        // 检查是否有推广用户
        if ($agent->promotedUsers()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => '该代理商有推广用户，无法删除',
            ], 422);
        }

        try {
            DB::beginTransaction();

            // 恢复用户角色
            User::where('id', $agent->user_id)->update(['role' => 'user']);

            // 删除代理商账户
            $agent->delete();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => '代理商删除成功',
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => '删除失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 更新代理商状态
     */
    public function updateStatus(Request $request, $id)
    {
        $agent = AgentAccount::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'status' => 'required|in:active,inactive,suspended,expired',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first(),
            ], 422);
        }

        $agent->update(['status' => $request->status]);

        return response()->json([
            'success' => true,
            'message' => '状态更新成功',
        ]);
    }

    /**
     * 代理商续费
     */
    public function renew(Request $request, $id)
    {
        $agent = AgentAccount::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'validity_period' => 'required|in:week,month,quarter,half_year,year,permanent,custom',
            'custom_end_date' => 'nullable|date|after:today',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first(),
            ], 422);
        }

        // 设置新的有效期
        $agent->setValidityPeriod($request->validity_period, $request->custom_end_date);
        $agent->status = 'active';
        $agent->save();

        return response()->json([
            'success' => true,
            'data' => [
                'new_end_date' => $agent->end_date ? $agent->end_date->format('Y-m-d H:i:s') : null,
                'is_permanent' => $agent->is_permanent,
            ],
            'message' => '续费成功',
        ]);
    }

    /**
     * 审核代理商申请
     */
    public function approve(Request $request, $id)
    {
        $agent = AgentAccount::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'action' => 'required|in:approve,reject',
            'remark' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first(),
            ], 422);
        }

        if ($request->action === 'approve') {
            $agent->update([
                'status' => 'active',
                'remark' => $request->remark,
            ]);
            $message = '代理商审核通过';
        } else {
            $agent->update([
                'status' => 'inactive',
                'remark' => $request->remark,
            ]);
            $message = '代理商审核拒绝';
        }

        return response()->json([
            'success' => true,
            'message' => $message,
        ]);
    }

    /**
     * 获取代理商佣金记录
     */
    public function getCommissions(Request $request, $id)
    {
        $agent = AgentAccount::findOrFail($id);

        $query = $agent->commissionLogs()->with(['order:id,order_no,amount']);

        // 时间筛选
        if ($request->filled('start_date')) {
            $query->whereDate('created_at', '>=', $request->start_date);
        }
        if ($request->filled('end_date')) {
            $query->whereDate('created_at', '<=', $request->end_date);
        }

        // 状态筛选
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        $commissions = $query->orderBy('created_at', 'desc')
                            ->paginate($request->input('limit', 20));

        return response()->json([
            'success' => true,
            'data' => $commissions,
        ]);
    }

    /**
     * 获取我的代理商信息
     */
    public function getMy(Request $request)
    {
        $user = $request->user();
        $agent = AgentAccount::where('user_id', $user->id)->with([
            'substation:id,name',
            'parentAgent:id,agent_name',
            'childAgents:id,agent_name,status'
        ])->first();

        if (!$agent) {
            return response()->json([
                'success' => false,
                'message' => '您不是代理商',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $agent,
        ]);
    }

    /**
     * 获取我的代理商统计
     */
    public function getMyStats(Request $request)
    {
        $user = $request->user();
        $agent = AgentAccount::where('user_id', $user->id)->first();

        if (!$agent) {
            return response()->json([
                'success' => false,
                'message' => '您不是代理商',
            ], 404);
        }

        $stats = [
            'total_users' => $agent->promotedUsers()->count(),
            'active_users' => $agent->promotedUsers()->where('status', 1)->count(),
            'total_commission' => $agent->total_commission,
            'monthly_commission' => $agent->commissionLogs()->whereMonth('created_at', now()->month)->sum('commission_amount'),
            'pending_commission' => $agent->commissionLogs()->where('status', 'pending')->sum('commission_amount'),
            'child_agents_count' => $agent->childAgents()->count(),
            'remaining_days' => $agent->getRemainingDays(),
            'commission_rate' => $agent->commission_rate,
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }

    /**
     * 更新我的代理商信息
     */
    public function updateMy(Request $request)
    {
        $user = $request->user();
        $agent = AgentAccount::where('user_id', $user->id)->first();

        if (!$agent) {
            return response()->json([
                'success' => false,
                'message' => '您不是代理商',
            ], 404);
        }

        $validator = Validator::make($request->all(), [
            'agent_name' => 'required|string|max:100',
            'remark' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first(),
            ], 422);
        }

        $agent->update($request->only(['agent_name', 'remark']));

        return response()->json([
            'success' => true,
            'data' => $agent,
            'message' => '信息更新成功',
        ]);
    }

    /**
     * 批量更新代理商状态
     */
    public function batchUpdateStatus(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'agent_ids' => 'required|array',
            'agent_ids.*' => 'exists:agent_accounts,id',
            'status' => 'required|in:active,inactive,suspended,expired',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first(),
            ], 422);
        }

        $updatedCount = AgentAccount::whereIn('id', $request->agent_ids)
                                   ->update(['status' => $request->status]);

        return response()->json([
            'success' => true,
            'message' => "成功更新 {$updatedCount} 个代理商状态",
            'updated_count' => $updatedCount,
        ]);
    }

    /**
     * 获取可选的上级代理商列表
     */
    public function getAvailableParents(Request $request)
    {
        $agentId = $request->input('agent_id');
        $agentGrade = $request->input('agent_grade', 1);
        
        $query = AgentAccount::where('status', 'active')
                            ->where('agent_grade', '<', $agentGrade);

        if ($agentId) {
            $query->where('id', '!=', $agentId);
        }

        $parents = $query->select('id', 'agent_name', 'agent_code', 'agent_grade')
                        ->orderBy('agent_grade')
                        ->orderBy('agent_name')
                        ->get();

        return response()->json([
            'success' => true,
            'data' => $parents,
        ]);
    }

    /**
     * 获取代理商层级结构
     */
    public function getHierarchy(Request $request)
    {
        try {
            $user = $request->user();
            
            // 根据用户角色获取层级数据
            $query = AgentAccount::with(['childAgents' => function($q) {
                $q->with(['childAgents' => function($q2) {
                    $q2->with('childAgents');
                }]);
            }]);
            
            // 如果是分站管理员，只显示其分站的代理商
            if ($user->hasRole('substation')) {
                $query->where('substation_id', $user->substation_id);
            }
            
            // 只获取顶级代理商（没有上级的）
            $agents = $query->whereNull('parent_agent_id')
                           ->where('status', 'active')
                           ->get();
            
            // 转换为树形结构
            $hierarchyData = $agents->map(function($agent) {
                return $this->buildAgentTree($agent);
            });

            return response()->json([
                'success' => true,
                'data' => [
                    'data' => $hierarchyData,
                    'total' => $agents->count()
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取层级数据失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 获取代理商层级统计
     */
    public function getHierarchyStats(Request $request)
    {
        try {
            $user = $request->user();
            $query = AgentAccount::query();
            
            // 根据用户角色过滤数据
            if ($user->hasRole('substation')) {
                $query->where('substation_id', $user->substation_id);
            }
            
            $totalAgents = $query->count();
            $directChildren = (clone $query)->whereNull('parent_agent_id')->count();
            
            // 计算最大层级深度
            $maxLevel = $this->calculateMaxLevel();
            
            // 计算总佣金
            $totalCommission = (clone $query)->sum('total_commission');

            return response()->json([
                'success' => true,
                'data' => [
                    'total_agents' => $totalAgents,
                    'direct_children' => $directChildren,
                    'max_level' => $maxLevel,
                    'total_commission' => $totalCommission,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取统计数据失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 获取代理商业绩数据
     */
    public function getPerformance(Request $request, $id)
    {
        try {
            $agent = AgentAccount::findOrFail($id);
            
            // 权限检查
            $user = $request->user();
            if ($user->hasRole('substation') && $agent->substation_id !== $user->substation_id) {
                return response()->json([
                    'success' => false,
                    'message' => '无权限查看此代理商业绩',
                ], 403);
            }

            // 模拟业绩数据 - 实际项目中应该根据业务逻辑计算
            $performanceData = [
                'total_orders' => 156,
                'total_sales' => 45680.00,
                'conversion_rate' => 12.8,
                'customer_count' => 89,
                'team_performance' => 78.5,
                'ranking' => 5,
                'recent_orders' => [
                    [
                        'id' => 1,
                        'order_no' => 'ORD202401001',
                        'amount' => 299.00,
                        'customer_name' => '张三',
                        'status' => 'completed',
                        'created_at' => '2024-01-15 10:30:00'
                    ],
                    [
                        'id' => 2,
                        'order_no' => 'ORD202401002',
                        'amount' => 199.00,
                        'customer_name' => '李四',
                        'status' => 'processing',
                        'created_at' => '2024-01-16 14:20:00'
                    ]
                ]
            ];

            return response()->json([
                'success' => true,
                'data' => $performanceData,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取业绩数据失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 转移代理商
     */
    public function transfer(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'new_parent_id' => 'nullable|exists:agent_accounts,id',
            'reason' => 'required|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first(),
            ], 422);
        }

        try {
            $agent = AgentAccount::findOrFail($id);
            
            // 权限检查
            $user = $request->user();
            if ($user->hasRole('substation') && $agent->substation_id !== $user->substation_id) {
                return response()->json([
                    'success' => false,
                    'message' => '无权限转移此代理商',
                ], 403);
            }

            DB::beginTransaction();

            // 记录转移操作
            $oldParentId = $agent->parent_agent_id;
            $agent->parent_agent_id = $request->new_parent_id;
            $agent->transfer_reason = $request->reason;
            $agent->transferred_at = now();
            $agent->transferred_by = $user->id;
            $agent->save();

            // 可以在这里添加转移日志记录

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => '代理商转移成功',
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => '转移失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 导出代理商数据
     */
    public function export(Request $request)
    {
        try {
            $user = $request->user();
            $query = AgentAccount::with(['user', 'substation', 'parentAgent']);
            
            // 根据用户角色过滤数据
            if ($user->hasRole('substation')) {
                $query->where('substation_id', $user->substation_id);
            }
            
            $agents = $query->get();
            
            // 这里应该使用 Laravel Excel 或类似的库来生成 Excel 文件
            // 为了演示，我们返回 CSV 格式的数据
            
            $csvData = "代理商编号,代理商姓名,等级,手机号,邮箱,状态,累计佣金,创建时间\n";
            
            foreach ($agents as $agent) {
                $csvData .= sprintf(
                    "%s,%s,%s,%s,%s,%s,%s,%s\n",
                    $agent->agent_code,
                    $agent->agent_name,
                    $agent->agent_level,
                    $agent->user->phone ?? '',
                    $agent->user->email ?? '',
                    $agent->status === 'active' ? '正常' : '停用',
                    $agent->total_commission,
                    $agent->created_at->format('Y-m-d H:i:s')
                );
            }
            
            return response($csvData)
                ->header('Content-Type', 'text/csv')
                ->header('Content-Disposition', 'attachment; filename="agents_export_' . date('Y-m-d') . '.csv"');

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '导出失败: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 构建代理商树形结构
     */
    private function buildAgentTree($agent)
    {
        $agentData = [
            'id' => $agent->id,
            'agent_name' => $agent->agent_name,
            'agent_code' => $agent->agent_code,
            'agent_level' => $agent->agent_level,
            'phone' => $agent->user->phone ?? '',
            'email' => $agent->user->email ?? '',
            'avatar' => $agent->user->avatar ?? '',
            'status' => $agent->status,
            'children_count' => $agent->childAgents->count(),
            'total_commission' => $agent->total_commission ?? 0,
            'month_commission' => 0, // 这里可以添加本月佣金计算逻辑
            'total_orders' => 0, // 这里可以添加订单数量计算逻辑
            'month_orders' => 0,
            'team_count' => $this->calculateTeamCount($agent),
            'created_at' => $agent->created_at->format('Y-m-d'),
            'children' => []
        ];

        // 递归处理子代理商
        foreach ($agent->childAgents as $child) {
            $agentData['children'][] = $this->buildAgentTree($child);
        }

        return $agentData;
    }

    /**
     * 计算团队总人数
     */
    private function calculateTeamCount($agent)
    {
        $count = 1; // 包含自己
        
        foreach ($agent->childAgents as $child) {
            $count += $this->calculateTeamCount($child);
        }
        
        return $count;
    }

    /**
     * 计算最大层级深度
     */
    private function calculateMaxLevel()
    {
        // 这里可以使用递归查询或者遍历的方式计算最大层级深度
        // 简化版本，返回固定值
        return 5;
    }
}