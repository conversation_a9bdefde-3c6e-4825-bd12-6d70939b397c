import request from '@/utils/request'
import { partnerApi } from '@/api/partner'

// 代理商相关API
export const agentApi = {
  // 获取我的代理商信息
  getMy() {
    return request({
      url: '/admin/agents/my',
      method: 'get'
    })
  },

  // 获取我的统计数据
  getMyStats() {
    return request({
      url: '/admin/agents/my-stats',
      method: 'get'
    })
  },

  // 获取代理商列表
  getList(params) {
    return request({
      url: '/admin/agents',
      method: 'get',
      params
    })
  },

  // 获取代理商详情
  getDetail(id) {
    return request({
      url: `/admin/agents/${id}`,
      method: 'get'
    })
  },

  // 创建代理商
  create(data) {
    return request({
      url: '/admin/agents',
      method: 'post',
      data
    })
  },

  // 更新代理商
  update(id, data) {
    return request({
      url: `/admin/agents/${id}`,
      method: 'put',
      data
    })
  },

  // 删除代理商
  delete(id) {
    return request({
      url: `/admin/agents/${id}`,
      method: 'delete'
    })
  },

  // 更新代理商状态
  updateStatus(id, status) {
    return request({
      url: `/admin/agents/${id}/status`,
      method: 'put',
      data: { status }
    })
  },

  // 获取层级结构数据
  getHierarchy(params) {
    return request({
      url: '/admin/agents/hierarchy',
      method: 'get',
      params
    })
  },

  // 获取团队统计数据
  getHierarchyStats() {
    return request({
      url: '/admin/agents/hierarchy/stats',
      method: 'get'
    })
  },

  // 获取代理商佣金数据
  getCommissions(id, params) {
    return request({
      url: `/admin/agents/${id}/commissions`,
      method: 'get',
      params
    })
  },

  // 获取代理商业绩数据
  getPerformance(id, params) {
    return request({
      url: `/admin/agents/${id}/performance`,
      method: 'get',
      params
    })
  },

  // 转移代理商
  transfer(id, data) {
    return request({
      url: `/admin/agents/${id}/transfer`,
      method: 'post',
      data
    })
  },

  // 获取可选的上级代理商
  getAvailableParents(params) {
    return request({
      url: '/admin/agents/available-parents',
      method: 'get',
      params
    })
  },

  // 导出代理商数据
  export(params) {
    return request({
      url: '/admin/agents/export',
      method: 'get',
      params,
      responseType: 'blob'
    })
  }
}

// 过渡适配：统一列表到 partnerApi
agentApi.getList = (params = {}) => partnerApi.getPartners({ ...params, type: params.type || 'agent' })
agentApi.getCommissionData = (params = {}) => partnerApi.getCommissions({ ...params, type: params.type || 'agent' })

// 为了兼容性，也导出单独的函数
export const getMy = agentApi.getMy
export const getMyStats = agentApi.getMyStats
export const getList = agentApi.getList
export const getDetail = agentApi.getDetail
export const create = agentApi.create
export const update = agentApi.update
export const deleteAgent = agentApi.delete
export const getTeamData = agentApi.getTeamData
export const getCommissionData = agentApi.getCommissionData

// 代理商申请相关API
export const agentApplicationApi = {
  // 获取申请统计
  getStats() {
    return request({
      url: '/admin/agent-applications/stats',
      method: 'get'
    })
  },

  // 获取申请列表
  getList(params) {
    return request({
      url: '/admin/agent-applications/',
      method: 'get',
      params
    })
  },

  // 获取申请详情
  getDetail(id) {
    return request({
      url: `/admin/agent-applications/${id}`,
      method: 'get'
    })
  },

  // 审核申请
  review(id, data) {
    return request({
      url: `/admin/agent-applications/${id}/review`,
      method: 'post',
      data
    })
  },

  // 批量审核
  batchReview(data) {
    return request({
      url: '/admin/agent-applications/batch-review',
      method: 'post',
      data
    })
  }
}

export default agentApi
