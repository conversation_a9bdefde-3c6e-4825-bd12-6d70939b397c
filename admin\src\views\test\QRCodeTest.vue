<template>
  <div class="qr-code-test-page">
    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <span>二维码功能测试</span>
          <el-tag type="success">修复完成</el-tag>
        </div>
      </template>

      <div class="test-section">
        <h3>测试输入</h3>
        <el-form :model="testForm" label-width="120px">
          <el-form-item label="测试文本">
            <el-input 
              v-model="testForm.text" 
              placeholder="输入要生成二维码的文本或URL"
              clearable
            />
          </el-form-item>
          <el-form-item label="二维码尺寸">
            <el-slider v-model="testForm.size" :min="100" :max="500" show-input />
          </el-form-item>
          <el-form-item label="边距">
            <el-slider v-model="testForm.margin" :min="0" :max="10" show-input />
          </el-form-item>
          <el-form-item label="纠错级别">
            <el-select v-model="testForm.errorCorrection">
              <el-option label="低 (L)" value="L" />
              <el-option label="中 (M)" value="M" />
              <el-option label="高 (Q)" value="Q" />
              <el-option label="最高 (H)" value="H" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <div class="test-section">
        <h3>测试结果</h3>
        <div class="test-results">
          <!-- 方法1：使用新的QRCodeCanvas组件 -->
          <div class="test-item">
            <h4>方法1：QRCodeCanvas组件</h4>
            <QRCodeCanvas 
              ref="qrCanvas1"
              :text="testForm.text"
              :size="testForm.size"
              :margin="testForm.margin"
              :error-correction-level="testForm.errorCorrection"
              :auto-generate="false"
              @generated="onGenerated1"
              @error="onError1"
            />
            <div class="test-actions">
              <el-button type="primary" @click="generate1">生成</el-button>
              <el-button @click="download1">下载</el-button>
            </div>
            <div class="test-status">
              <el-tag v-if="status1.success" type="success">{{ status1.message }}</el-tag>
              <el-tag v-else-if="status1.error" type="danger">{{ status1.message }}</el-tag>
            </div>
          </div>

          <!-- 方法2：传统Canvas方式 -->
          <div class="test-item">
            <h4>方法2：传统Canvas</h4>
            <div class="canvas-container">
              <canvas 
                ref="qrCanvas2" 
                :width="testForm.size" 
                :height="testForm.size"
                style="border: 1px solid #ddd;"
              ></canvas>
            </div>
            <div class="test-actions">
              <el-button type="primary" @click="generate2">生成</el-button>
              <el-button @click="download2">下载</el-button>
            </div>
            <div class="test-status">
              <el-tag v-if="status2.success" type="success">{{ status2.message }}</el-tag>
              <el-tag v-else-if="status2.error" type="danger">{{ status2.message }}</el-tag>
            </div>
          </div>

          <!-- 方法3：DataURL方式 -->
          <div class="test-item">
            <h4>方法3：DataURL图片</h4>
            <div class="image-container">
              <img v-if="dataUrl3" :src="dataUrl3" :style="`width: ${testForm.size}px; height: ${testForm.size}px;`" />
              <div v-else class="placeholder">点击生成查看结果</div>
            </div>
            <div class="test-actions">
              <el-button type="primary" @click="generate3">生成</el-button>
              <el-button @click="download3">下载</el-button>
            </div>
            <div class="test-status">
              <el-tag v-if="status3.success" type="success">{{ status3.message }}</el-tag>
              <el-tag v-else-if="status3.error" type="danger">{{ status3.message }}</el-tag>
            </div>
          </div>
        </div>
      </div>

      <div class="test-section">
        <h3>预设测试用例</h3>
        <div class="preset-tests">
          <el-button @click="testUrl">测试URL</el-button>
          <el-button @click="testText">测试文本</el-button>
          <el-button @click="testLongText">测试长文本</el-button>
          <el-button @click="testChinese">测试中文</el-button>
          <el-button @click="testSpecialChars">测试特殊字符</el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import QRCode from 'qrcode'
import QRCodeCanvas from '@/components/common/QRCodeCanvas.vue'

// 测试表单
const testForm = reactive({
  text: 'https://example.com/test',
  size: 200,
  margin: 2,
  errorCorrection: 'M'
})

// 组件引用
const qrCanvas1 = ref(null)
const qrCanvas2 = ref(null)
const dataUrl3 = ref('')

// 状态跟踪
const status1 = reactive({ success: false, error: false, message: '' })
const status2 = reactive({ success: false, error: false, message: '' })
const status3 = reactive({ success: false, error: false, message: '' })

// 方法1：使用QRCodeCanvas组件
const generate1 = () => {
  if (!testForm.text) {
    ElMessage.warning('请输入测试文本')
    return
  }
  resetStatus(status1)
  qrCanvas1.value?.generateQRCode()
}

const onGenerated1 = (data) => {
  status1.success = true
  status1.message = data.fallback ? '生成成功（备用方案）' : '生成成功'
}

const onError1 = (error) => {
  status1.error = true
  status1.message = error.error?.message || '生成失败'
}

const download1 = () => {
  qrCanvas1.value?.download(`test_qr_method1.png`)
}

// 方法2：传统Canvas方式
const generate2 = async () => {
  if (!testForm.text) {
    ElMessage.warning('请输入测试文本')
    return
  }
  
  resetStatus(status2)
  
  try {
    const canvas = qrCanvas2.value
    const ctx = canvas.getContext('2d')
    ctx.clearRect(0, 0, canvas.width, canvas.height)
    
    await QRCode.toCanvas(canvas, testForm.text, {
      width: testForm.size,
      height: testForm.size,
      margin: testForm.margin,
      errorCorrectionLevel: testForm.errorCorrection
    })
    
    status2.success = true
    status2.message = '生成成功'
  } catch (error) {
    console.error('Method 2 failed:', error)
    status2.error = true
    status2.message = error.message || '生成失败'
  }
}

const download2 = () => {
  const canvas = qrCanvas2.value
  if (canvas) {
    const link = document.createElement('a')
    link.href = canvas.toDataURL('image/png')
    link.download = 'test_qr_method2.png'
    link.click()
    ElMessage.success('下载成功')
  }
}

// 方法3：DataURL方式
const generate3 = async () => {
  if (!testForm.text) {
    ElMessage.warning('请输入测试文本')
    return
  }
  
  resetStatus(status3)
  
  try {
    const dataUrl = await QRCode.toDataURL(testForm.text, {
      width: testForm.size,
      margin: testForm.margin,
      errorCorrectionLevel: testForm.errorCorrection
    })
    
    dataUrl3.value = dataUrl
    status3.success = true
    status3.message = '生成成功'
  } catch (error) {
    console.error('Method 3 failed:', error)
    status3.error = true
    status3.message = error.message || '生成失败'
    dataUrl3.value = ''
  }
}

const download3 = () => {
  if (dataUrl3.value) {
    const link = document.createElement('a')
    link.href = dataUrl3.value
    link.download = 'test_qr_method3.png'
    link.click()
    ElMessage.success('下载成功')
  }
}

// 工具函数
const resetStatus = (status) => {
  status.success = false
  status.error = false
  status.message = ''
}

// 预设测试用例
const testUrl = () => {
  testForm.text = 'https://www.example.com/promotion/link/12345'
}

const testText = () => {
  testForm.text = 'Hello World! This is a test.'
}

const testLongText = () => {
  testForm.text = 'This is a very long text to test the QR code generation with a lot of content that might cause issues with encoding or display. Let us see how it handles this scenario.'
}

const testChinese = () => {
  testForm.text = '这是一个中文测试，包含特殊字符：！@#￥%……&*（）'
}

const testSpecialChars = () => {
  testForm.text = '!@#$%^&*()_+-=[]{}|;:,.<>?/~`'
}
</script>

<style scoped>
.qr-code-test-page {
  padding: 20px;
}

.test-card {
  max-width: 1200px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.test-section {
  margin-bottom: 30px;
}

.test-section h3 {
  margin-bottom: 15px;
  color: #303133;
}

.test-results {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.test-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 15px;
  text-align: center;
}

.test-item h4 {
  margin-bottom: 15px;
  color: #606266;
}

.canvas-container,
.image-container {
  margin: 15px 0;
  display: flex;
  justify-content: center;
}

.placeholder {
  width: 200px;
  height: 200px;
  border: 2px dashed #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
}

.test-actions {
  margin: 15px 0;
}

.test-actions .el-button {
  margin: 0 5px;
}

.test-status {
  margin-top: 10px;
}

.preset-tests {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}
</style>
