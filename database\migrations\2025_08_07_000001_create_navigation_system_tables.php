<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * 导航系统相关表迁移
     */
    public function up()
    {
        // 1. 导航菜单配置表
        Schema::create('navigation_menus', function (Blueprint $table) {
            $table->id();
            $table->string('code', 50)->unique()->comment('菜单代码');
            $table->string('name', 100)->comment('菜单名称');
            $table->string('domain', 20)->comment('域分类：core,operation,analytics,system');
            $table->string('icon', 100)->nullable()->comment('图标');
            $table->string('route', 200)->nullable()->comment('路由地址');
            $table->string('component', 200)->nullable()->comment('组件路径');
            $table->json('meta')->nullable()->comment('元数据(权限、标题等)');
            $table->integer('parent_id')->default(0)->comment('父级菜单ID');
            $table->integer('sort_order')->default(0)->comment('排序');
            $table->boolean('is_visible')->default(true)->comment('是否显示');
            $table->boolean('is_cacheable')->default(true)->comment('是否可缓存');
            $table->string('permission')->nullable()->comment('所需权限');
            $table->json('roles')->nullable()->comment('适用角色');
            $table->timestamps();
            
            $table->index(['domain', 'is_visible', 'sort_order']);
            $table->index(['parent_id', 'sort_order']);
            $table->index(['permission']);
        });

        // 2. 用户导航偏好设置表
        Schema::create('user_navigation_preferences', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->cascadeOnDelete();
            $table->json('pinned_menus')->nullable()->comment('固定菜单列表');
            $table->json('hidden_menus')->nullable()->comment('隐藏菜单列表');
            $table->json('menu_order')->nullable()->comment('菜单自定义顺序');
            $table->json('domain_preferences')->nullable()->comment('域偏好设置');
            $table->json('layout_settings')->nullable()->comment('布局设置');
            $table->json('quick_access')->nullable()->comment('快捷访问菜单');
            $table->boolean('enable_smart_recommend')->default(true)->comment('启用智能推荐');
            $table->string('default_domain', 20)->default('core')->comment('默认显示域');
            $table->timestamps();
            
            $table->unique('user_id');
            $table->index(['user_id', 'enable_smart_recommend']);
        });

        // 3. 导航使用统计表
        Schema::create('navigation_usage_stats', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->cascadeOnDelete();
            $table->string('menu_code', 50)->comment('菜单代码');
            $table->string('domain', 20)->comment('所属域');
            $table->integer('visit_count')->default(0)->comment('访问次数');
            $table->timestamp('last_visited_at')->nullable()->comment('最后访问时间');
            $table->integer('total_duration')->default(0)->comment('总停留时间(秒)');
            $table->integer('avg_duration')->default(0)->comment('平均停留时间(秒)');
            $table->json('access_patterns')->nullable()->comment('访问模式分析');
            $table->date('date')->comment('统计日期');
            $table->timestamps();
            
            $table->unique(['user_id', 'menu_code', 'date']);
            $table->index(['user_id', 'date']);
            $table->index(['menu_code', 'date']);
            $table->index(['domain', 'date']);
        });

        // 4. 智能推荐记录表
        Schema::create('navigation_recommendations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->cascadeOnDelete();
            $table->string('menu_code', 50)->comment('推荐的菜单代码');
            $table->string('recommendation_type', 30)->comment('推荐类型：frequency,pattern,similar_user');
            $table->decimal('score', 5, 3)->comment('推荐分数');
            $table->json('factors')->nullable()->comment('推荐因子');
            $table->boolean('is_clicked')->default(false)->comment('是否被点击');
            $table->timestamp('recommended_at')->comment('推荐时间');
            $table->timestamp('clicked_at')->nullable()->comment('点击时间');
            $table->timestamps();
            
            $table->index(['user_id', 'recommended_at']);
            $table->index(['menu_code', 'recommendation_type']);
            $table->index(['is_clicked']);
        });

        // 5. 全局搜索记录表
        Schema::create('navigation_search_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->cascadeOnDelete();
            $table->string('query', 200)->comment('搜索关键词');
            $table->json('results')->nullable()->comment('搜索结果');
            $table->integer('result_count')->default(0)->comment('结果数量');
            $table->string('selected_result', 50)->nullable()->comment('用户选择的结果');
            $table->timestamp('searched_at')->comment('搜索时间');
            $table->string('ip_address', 45)->nullable()->comment('IP地址');
            $table->string('user_agent', 500)->nullable()->comment('用户代理');
            $table->timestamps();
            
            $table->index(['user_id', 'searched_at']);
            // SQLite 不支持全文索引，使用普通索引替代
            $table->index(['query']);
            // $table->fullText(['query']);
        });

        // 6. 角色权限映射表
        Schema::create('role_navigation_permissions', function (Blueprint $table) {
            $table->id();
            $table->string('role', 50)->comment('角色名称');
            $table->json('allowed_menus')->comment('允许访问的菜单代码列表');
            $table->json('denied_menus')->nullable()->comment('明确拒绝的菜单代码列表');
            $table->json('domain_access')->comment('域访问权限');
            $table->json('feature_permissions')->nullable()->comment('功能权限配置');
            $table->boolean('is_active')->default(true)->comment('是否启用');
            $table->timestamps();
            
            $table->unique('role');
            $table->index(['role', 'is_active']);
        });

        // 7. 系统配置表(导航相关)
        Schema::create('navigation_system_configs', function (Blueprint $table) {
            $table->id();
            $table->string('key', 100)->unique()->comment('配置键');
            $table->text('value')->comment('配置值');
            $table->string('type', 20)->default('string')->comment('数据类型');
            $table->string('group', 50)->default('navigation')->comment('配置分组');
            $table->text('description')->nullable()->comment('配置说明');
            $table->boolean('is_public')->default(false)->comment('是否为公开配置');
            $table->timestamps();
            
            $table->index(['group', 'key']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('navigation_system_configs');
        Schema::dropIfExists('role_navigation_permissions');
        Schema::dropIfExists('navigation_search_logs');
        Schema::dropIfExists('navigation_recommendations');
        Schema::dropIfExists('navigation_usage_stats');
        Schema::dropIfExists('user_navigation_preferences');
        Schema::dropIfExists('navigation_menus');
    }
};