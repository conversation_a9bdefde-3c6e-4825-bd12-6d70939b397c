<template>
  <div class="qrcode-generator">
    <!-- 二维码显示区域 -->
    <div class="qrcode-container" :class="containerClass">
      <div v-if="loading" class="flex items-center justify-center p-8">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      </div>
      
      <div v-else-if="qrCodeDataUrl" class="flex flex-col items-center space-y-4">
        <!-- 二维码图片 -->
        <div class="bg-white p-4 rounded-lg shadow-sm border">
          <img 
            :src="qrCodeDataUrl" 
            :alt="`${title}二维码`" 
            class="block"
            :style="{ width: `${size}px`, height: `${size}px` }"
          />
        </div>
        
        <!-- 二维码标题 -->
        <div v-if="title" class="text-center">
          <p class="text-sm font-medium text-gray-900">{{ title }}</p>
          <p v-if="subtitle" class="text-xs text-gray-500 mt-1">{{ subtitle }}</p>
        </div>
        
        <!-- 操作按钮 -->
        <div class="flex items-center space-x-2">
          <button 
            @click="downloadQRCode"
            class="flex items-center space-x-1 px-3 py-2 text-xs font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3M7 7h10a2 2 0 012 2v10a2 2 0 01-2 2H7a2 2 0 01-2-2V9a2 2 0 012-2z" />
            </svg>
            <span>下载</span>
          </button>
          
          <button 
            @click="copyQRCode"
            class="flex items-center space-x-1 px-3 py-2 text-xs font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
            </svg>
            <span>复制</span>
          </button>
          
          <button 
            v-if="showShareButton"
            @click="shareQRCode"
            class="flex items-center space-x-1 px-3 py-2 text-xs font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
            </svg>
            <span>分享</span>
          </button>
        </div>
        
        <!-- 二维码链接 -->
        <div v-if="showUrl" class="text-center max-w-xs">
          <p class="text-xs text-gray-500 break-all">{{ text }}</p>
        </div>
      </div>
      
      <div v-else-if="error" class="text-center p-8">
        <div class="text-red-500 mb-2">
          <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <p class="text-sm text-red-600">{{ error }}</p>
        <button 
          @click="generateQRCode"
          class="mt-3 px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-md transition-colors"
        >
          重新生成
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import QRCode from 'qrcode'

// 组件属性
interface Props {
  text: string // 要生成二维码的文本
  title?: string // 二维码标题
  subtitle?: string // 二维码副标题
  size?: number // 二维码尺寸
  containerClass?: string // 容器样式类
  showUrl?: boolean // 是否显示原始链接
  showShareButton?: boolean // 是否显示分享按钮
  autoGenerate?: boolean // 是否自动生成
  errorCorrectionLevel?: 'L' | 'M' | 'Q' | 'H' // 纠错级别
  margin?: number // 边距
  color?: {
    dark?: string // 前景色
    light?: string // 背景色
  }
}

const props = withDefaults(defineProps<Props>(), {
  size: 200,
  showUrl: false,
  showShareButton: false,
  autoGenerate: true,
  errorCorrectionLevel: 'M',
  margin: 4,
  color: () => ({
    dark: '#000000',
    light: '#FFFFFF'
  })
})

// 组件事件
const emit = defineEmits<{
  generated: [dataUrl: string]
  error: [error: string]
  download: [dataUrl: string]
  copy: [dataUrl: string]
  share: [dataUrl: string]
}>()

// 响应式数据
const loading = ref(false)
const qrCodeDataUrl = ref<string>('')
const error = ref<string>('')

// 生成二维码配置
const qrCodeOptions = computed(() => ({
  width: props.size,
  height: props.size,
  margin: props.margin,
  color: props.color,
  errorCorrectionLevel: props.errorCorrectionLevel
}))

// 生成二维码
const generateQRCode = async () => {
  if (!props.text) {
    error.value = '请提供要生成二维码的内容'
    return
  }

  loading.value = true
  error.value = ''

  try {
    // 验证URL格式
    if (props.text.startsWith('http') && !isValidUrl(props.text)) {
      throw new Error('无效的URL格式')
    }

    const dataUrl = await QRCode.toDataURL(props.text, qrCodeOptions.value)
    qrCodeDataUrl.value = dataUrl
    emit('generated', dataUrl)
  } catch (err) {
    console.error('QR Code generation failed:', err)
    const errorMessage = err instanceof Error ? err.message : '生成二维码失败'
    error.value = errorMessage
    emit('error', errorMessage)

    // 尝试使用备用方案
    if (props.text.startsWith('http')) {
      await tryFallbackGeneration()
    }
  } finally {
    loading.value = false
  }
}

// URL验证函数
const isValidUrl = (string: string): boolean => {
  try {
    new URL(string)
    return true
  } catch (_) {
    return false
  }
}

// 备用二维码生成方案
const tryFallbackGeneration = async () => {
  try {
    const fallbackUrl = `https://api.qrserver.com/v1/create-qr-code/?size=${props.size}x${props.size}&data=${encodeURIComponent(props.text)}`
    qrCodeDataUrl.value = fallbackUrl
    error.value = '' // 清除错误信息
    emit('generated', fallbackUrl)
  } catch (fallbackErr) {
    console.error('Fallback QR generation failed:', fallbackErr)
    error.value = '二维码生成失败，请检查网络连接或稍后重试'
  }
}

// 下载二维码
const downloadQRCode = () => {
  if (!qrCodeDataUrl.value) return
  
  const link = document.createElement('a')
  link.href = qrCodeDataUrl.value
  link.download = `${props.title || '二维码'}.png`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  
  emit('download', qrCodeDataUrl.value)
}

// 复制二维码
const copyQRCode = async () => {
  if (!qrCodeDataUrl.value) return
  
  try {
    // 将 data URL 转换为 Blob
    const response = await fetch(qrCodeDataUrl.value)
    const blob = await response.blob()
    
    // 复制到剪贴板
    await navigator.clipboard.write([
      new ClipboardItem({
        [blob.type]: blob
      })
    ])
    
    // 显示成功提示
    showToast('二维码已复制到剪贴板')
    emit('copy', qrCodeDataUrl.value)
  } catch (err) {
    // 如果复制失败，尝试复制文本链接
    try {
      await navigator.clipboard.writeText(props.text)
      showToast('链接已复制到剪贴板')
    } catch (textErr) {
      showToast('复制失败，请手动复制', 'error')
    }
  }
}

// 分享二维码
const shareQRCode = async () => {
  if (!qrCodeDataUrl.value) return
  
  try {
    if (navigator.share) {
      // 使用原生分享 API
      const response = await fetch(qrCodeDataUrl.value)
      const blob = await response.blob()
      const file = new File([blob], `${props.title || '二维码'}.png`, { type: blob.type })
      
      await navigator.share({
        title: props.title || '二维码',
        text: props.subtitle || '扫描二维码',
        files: [file]
      })
    } else {
      // 降级到复制链接
      await navigator.clipboard.writeText(props.text)
      showToast('链接已复制，可以分享给好友')
    }
    
    emit('share', qrCodeDataUrl.value)
  } catch (err) {
    showToast('分享失败', 'error')
  }
}

// 显示提示消息
const showToast = (message: string, type: 'success' | 'error' = 'success') => {
  // 这里应该调用全局的toast组件
  console.log(`${type}: ${message}`)
  
  // 简单的提示实现
  const toast = document.createElement('div')
  toast.className = `fixed top-4 right-4 z-50 px-4 py-2 rounded-md text-white text-sm font-medium ${
    type === 'error' ? 'bg-red-500' : 'bg-green-500'
  } transform transition-all duration-300`
  toast.textContent = message
  document.body.appendChild(toast)
  
  setTimeout(() => {
    toast.style.transform = 'translateX(100%)'
    setTimeout(() => {
      document.body.removeChild(toast)
    }, 300)
  }, 2000)
}

// 监听文本变化，自动重新生成二维码
watch(() => props.text, (newText) => {
  if (newText && props.autoGenerate) {
    generateQRCode()
  }
}, { immediate: true })

// 暴露方法给父组件
defineExpose({
  generateQRCode,
  downloadQRCode,
  copyQRCode,
  shareQRCode
})
</script>

<style scoped>
.qrcode-generator {
  @apply w-full;
}

.qrcode-container {
  @apply bg-gray-50 rounded-lg border border-gray-200;
}

/* 二维码图片样式 */
.qrcode-container img {
  @apply rounded-md;
  image-rendering: pixelated;
  image-rendering: -moz-crisp-edges;
  image-rendering: crisp-edges;
}

/* 加载动画优化 */
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style> 