<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Models\WechatGroup;
use App\Models\DomainPool;
use App\Models\PromotionLink;
use App\Services\AntiBlockService;
use App\Services\AntiBlockLinkService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

/**
 * 防红系统管理控制器
 */
class AntiBlockController extends Controller
{
    protected AntiBlockService $antiBlockService;
    protected AntiBlockLinkService $antiBlockLinkService;

    public function __construct(
        AntiBlockService $antiBlockService,
        AntiBlockLinkService $antiBlockLinkService
    ) {
        $this->antiBlockService = $antiBlockService;
        $this->antiBlockLinkService = $antiBlockLinkService;
    }

    /**
     * 获取防红系统统计数据
     */
    public function getStats(): JsonResponse
    {
        return $this->overview();
    }

    /**
     * 获取防红系统概览
     */
    public function overview(): JsonResponse
    {
        try {
            $stats = $this->antiBlockService->getStats();
            $config = $this->antiBlockService->getConfig();

            // 获取最近的域名检查记录
            $recentChecks = \App\Models\DomainCheckLog::with('domainPool')
                ->orderBy('created_at', 'desc')
                ->limit(10)
                ->get();

            // 获取启用防红的群组统计
            $antiBlockGroups = WechatGroup::whereJsonContains('anti_block_config->enabled', true)->count();
            $totalGroups = WechatGroup::count();

            return $this->success([
                'stats' => $stats,
                'config' => $config,
                'recent_checks' => $recentChecks,
                'group_stats' => [
                    'total_groups' => $totalGroups,
                    'anti_block_groups' => $antiBlockGroups,
                    'coverage_rate' => $totalGroups > 0 ? round(($antiBlockGroups / $totalGroups) * 100, 2) : 0
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('获取防红系统概览失败', [
                'error' => $e->getMessage()
            ]);

            return $this->error('获取防红系统概览失败');
        }
    }

    /**
     * 批量检查域名状态
     */
    public function batchCheckDomains(Request $request): JsonResponse
    {
        $request->validate([
            'domain_pool_ids' => 'sometimes|array',
            'domain_pool_ids.*' => 'integer|exists:domain_pools,id',
            'check_all' => 'sometimes|boolean'
        ]);

        try {
            if ($request->check_all) {
                // 检查所有活跃域名池
                $result = $this->antiBlockService->batchIntelligentCheck();
            } else {
                // 检查指定域名池
                $domainPoolIds = $request->domain_pool_ids ?? [];
                $result = $this->antiBlockService->batchIntelligentCheck($domainPoolIds);
            }

            return $this->success($result, '域名检查完成');

        } catch (\Exception $e) {
            Log::error('批量检查域名失败', [
                'request_data' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return $this->error('批量检查域名失败：' . $e->getMessage());
        }
    }

    /**
     * 获取群组防红配置
     */
    public function getGroupAntiBlockConfig(int $groupId): JsonResponse
    {
        try {
            $group = WechatGroup::find($groupId);
            
            if (!$group) {
                return $this->error('群组不存在', null, 404);
            }

            $antiBlockConfig = $group->anti_block_config ?? [];
            $domainPoolId = $antiBlockConfig['domain_pool_id'] ?? null;
            
            $domainPool = null;
            if ($domainPoolId) {
                $domainPool = DomainPool::find($domainPoolId);
            }

            // 获取推广链接统计
            $promotionStats = $this->antiBlockLinkService->getPromotionStats($group);

            return $this->success([
                'group_id' => $group->id,
                'group_title' => $group->title,
                'anti_block_config' => $antiBlockConfig,
                'domain_pool' => $domainPool,
                'promotion_stats' => $promotionStats,
                'is_enabled' => ($antiBlockConfig['enabled'] ?? false) === true
            ]);

        } catch (\Exception $e) {
            Log::error('获取群组防红配置失败', [
                'group_id' => $groupId,
                'error' => $e->getMessage()
            ]);

            return $this->error('获取群组防红配置失败');
        }
    }

    /**
     * 更新群组防红配置
     */
    public function updateGroupAntiBlockConfig(Request $request, int $groupId): JsonResponse
    {
        $request->validate([
            'enabled' => 'required|boolean',
            'domain_pool_id' => 'required_if:enabled,true|nullable|integer|exists:domain_pools,id',
            'check_frequency' => 'sometimes|integer|in:5,10,30,60',
            'switch_strategy' => 'sometimes|string|in:immediate,delayed,manual'
        ]);

        try {
            $group = WechatGroup::find($groupId);
            
            if (!$group) {
                return $this->error('群组不存在', null, 404);
            }

            $antiBlockConfig = $group->anti_block_config ?? [];
            
            // 更新配置
            $antiBlockConfig['enabled'] = $request->enabled;
            $antiBlockConfig['check_frequency'] = $request->check_frequency ?? 10;
            $antiBlockConfig['switch_strategy'] = $request->switch_strategy ?? 'immediate';
            $antiBlockConfig['updated_at'] = now()->toDateTimeString();

            if ($request->enabled && $request->domain_pool_id) {
                // 启用防红系统
                $antiBlockConfig['domain_pool_id'] = $request->domain_pool_id;
                
                // 使用AntiBlockService分配域名池
                $assignResult = $this->antiBlockService->assignDomainPool($groupId, $request->domain_pool_id);
                
                if (!$assignResult) {
                    return $this->error('分配域名池失败');
                }
            } else {
                // 禁用防红系统
                $antiBlockConfig['domain_pool_id'] = null;
                
                // 移除域名池配置
                $this->antiBlockService->removeDomainPool($groupId);
            }

            $group->update(['anti_block_config' => $antiBlockConfig]);

            Log::info('更新群组防红配置成功', [
                'group_id' => $groupId,
                'enabled' => $request->enabled,
                'domain_pool_id' => $request->domain_pool_id
            ]);

            return $this->success([
                'group_id' => $groupId,
                'anti_block_config' => $antiBlockConfig
            ], '防红配置更新成功');

        } catch (\Exception $e) {
            Log::error('更新群组防红配置失败', [
                'group_id' => $groupId,
                'request_data' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return $this->error('更新防红配置失败：' . $e->getMessage());
        }
    }

    /**
     * 生成群组推广链接
     */
    public function generateGroupPromotionLink(Request $request, int $groupId): JsonResponse
    {
        $request->validate([
            'source' => 'sometimes|string|max:50',
            'utm_campaign' => 'sometimes|string|max:100',
            'utm_medium' => 'sometimes|string|max:50',
            'utm_source' => 'sometimes|string|max:50'
        ]);

        try {
            $group = WechatGroup::find($groupId);
            
            if (!$group) {
                return $this->error('群组不存在', null, 404);
            }

            $params = $request->only(['source', 'utm_campaign', 'utm_medium', 'utm_source']);
            
            $result = $this->antiBlockLinkService->generatePromotionLink($group, $params);

            return $this->success($result, '推广链接生成成功');

        } catch (\Exception $e) {
            Log::error('生成群组推广链接失败', [
                'group_id' => $groupId,
                'error' => $e->getMessage()
            ]);

            return $this->error('生成推广链接失败：' . $e->getMessage());
        }
    }

    /**
     * 检查并切换群组域名
     */
    public function checkAndSwitchGroupDomain(int $groupId): JsonResponse
    {
        try {
            $group = WechatGroup::find($groupId);
            
            if (!$group) {
                return $this->error('群组不存在', null, 404);
            }

            $result = $this->antiBlockLinkService->checkAndSwitchDomain($group);

            return $this->success($result, $result['message'] ?? '操作完成');

        } catch (\Exception $e) {
            Log::error('检查并切换群组域名失败', [
                'group_id' => $groupId,
                'error' => $e->getMessage()
            ]);

            return $this->error('操作失败：' . $e->getMessage());
        }
    }

    /**
     * 获取群组推广链接列表
     */
    public function getGroupPromotionLinks(Request $request, int $groupId): JsonResponse
    {
        $request->validate([
            'page' => 'sometimes|integer|min:1',
            'per_page' => 'sometimes|integer|min:1|max:100',
            'status' => 'sometimes|string|in:active,inactive,expired,all'
        ]);

        try {
            $group = WechatGroup::find($groupId);
            
            if (!$group) {
                return $this->error('群组不存在', null, 404);
            }

            $query = PromotionLink::where('group_id', $groupId);

            // 状态筛选
            $status = $request->get('status', 'all');
            if ($status !== 'all') {
                switch ($status) {
                    case 'active':
                        $query->where('is_active', true)
                              ->where('expires_at', '>', now());
                        break;
                    case 'inactive':
                        $query->where('is_active', false);
                        break;
                    case 'expired':
                        $query->where('expires_at', '<=', now());
                        break;
                }
            }

            $perPage = $request->get('per_page', 15);
            $links = $query->orderBy('created_at', 'desc')
                          ->paginate($perPage);

            // 添加额外信息
            $links->getCollection()->transform(function ($link) {
                $link->is_expired = $link->expires_at && $link->expires_at->isPast();
                $link->remaining_days = $link->expires_at ? max(0, $link->expires_at->diffInDays(now())) : null;
                return $link;
            });

            return $this->success([
                'links' => $links,
                'stats' => $this->antiBlockLinkService->getPromotionStats($group)
            ]);

        } catch (\Exception $e) {
            Log::error('获取群组推广链接列表失败', [
                'group_id' => $groupId,
                'error' => $e->getMessage()
            ]);

            return $this->error('获取推广链接列表失败');
        }
    }

    /**
     * 批量操作推广链接
     */
    public function batchOperatePromotionLinks(Request $request): JsonResponse
    {
        $request->validate([
            'link_ids' => 'required|array|min:1',
            'link_ids.*' => 'integer|exists:promotion_links,id',
            'action' => 'required|string|in:enable,disable,delete,extend_expiry'
        ]);

        try {
            $result = $this->antiBlockLinkService->batchUpdateLinkStatus(
                $request->link_ids,
                $request->action
            );

            return $this->success($result, $result['message'] ?? '批量操作完成');

        } catch (\Exception $e) {
            Log::error('批量操作推广链接失败', [
                'request_data' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return $this->error('批量操作失败：' . $e->getMessage());
        }
    }

    /**
     * 获取域名健康报告
     */
    public function getDomainHealthReport(int $domainPoolId): JsonResponse
    {
        try {
            $result = $this->antiBlockService->getDomainHealthReport($domainPoolId);

            if (!$result['success']) {
                return $this->error($result['message']);
            }

            return $this->success($result['data'], '获取域名健康报告成功');

        } catch (\Exception $e) {
            Log::error('获取域名健康报告失败', [
                'domain_pool_id' => $domainPoolId,
                'error' => $e->getMessage()
            ]);

            return $this->error('获取域名健康报告失败');
        }
    }

    /**
     * 更新防红系统配置
     */
    public function updateSystemConfig(Request $request): JsonResponse
    {
        $request->validate([
            'enabled' => 'sometimes|boolean',
            'default_url' => 'sometimes|string|url'
        ]);

        try {
            $this->antiBlockService->updateConfig($request->only(['enabled', 'default_url']));

            Log::info('更新防红系统配置成功', [
                'config_data' => $request->all(),
                'user_id' => auth()->id()
            ]);

            return $this->success(null, '防红系统配置更新成功');

        } catch (\Exception $e) {
            Log::error('更新防红系统配置失败', [
                'request_data' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return $this->error('更新防红系统配置失败');
        }
    }

    /**
     * 获取防红系统日志
     */
    public function getLogs(Request $request): JsonResponse
    {
        $request->validate([
            'page' => 'sometimes|integer|min:1',
            'per_page' => 'sometimes|integer|min:1|max:100',
            'domain_pool_id' => 'sometimes|integer|exists:domain_pools,id',
            'status' => 'sometimes|integer|in:1,2,3',
            'date_from' => 'sometimes|date',
            'date_to' => 'sometimes|date|after_or_equal:date_from'
        ]);

        try {
            $query = \App\Models\DomainCheckLog::with('domainPool');

            // 筛选条件
            if ($request->domain_pool_id) {
                $query->where('domain_pool_id', $request->domain_pool_id);
            }

            if ($request->status) {
                $query->where('status', $request->status);
            }

            if ($request->date_from) {
                $query->whereDate('created_at', '>=', $request->date_from);
            }

            if ($request->date_to) {
                $query->whereDate('created_at', '<=', $request->date_to);
            }

            $perPage = $request->get('per_page', 20);
            $logs = $query->orderBy('created_at', 'desc')
                         ->paginate($perPage);

            return $this->success($logs);

        } catch (\Exception $e) {
            Log::error('获取防红系统日志失败', [
                'request_data' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return $this->error('获取防红系统日志失败');
        }
    }

    // ==================== 域名管理相关方法 ====================

    /**
     * 获取域名列表
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = DomainPool::query();

            // 搜索过滤
            if ($request->search) {
                $query->where('domain', 'like', '%' . $request->search . '%')
                      ->orWhere('name', 'like', '%' . $request->search . '%');
            }

            // 状态过滤
            if ($request->status) {
                $query->where('status', $request->status);
            }

            $perPage = $request->get('per_page', 15);
            $domains = $query->orderBy('created_at', 'desc')->paginate($perPage);

            return $this->success($domains);

        } catch (\Exception $e) {
            Log::error('获取域名列表失败', ['error' => $e->getMessage()]);
            return $this->error('获取域名列表失败');
        }
    }

    /**
     * 创建域名
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'name' => 'required|string|max:100',
            'domain' => 'required|string|max:255|unique:domain_pools,domain',
            'priority' => 'sometimes|integer|min:1|max:100',
            'description' => 'sometimes|string|max:500'
        ]);

        try {
            $domain = DomainPool::create([
                'name' => $request->name,
                'domain' => $request->domain,
                'priority' => $request->priority ?? 50,
                'description' => $request->description,
                'status' => 1,
                'health_score' => 100,
                'use_count' => 0,
                'check_count' => 0
            ]);

            return $this->success($domain, '域名创建成功');

        } catch (\Exception $e) {
            Log::error('创建域名失败', [
                'request_data' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return $this->error('创建域名失败');
        }
    }

    /**
     * 获取域名详情
     */
    public function show(int $id): JsonResponse
    {
        try {
            $domain = DomainPool::find($id);
            
            if (!$domain) {
                return $this->error('域名不存在', null, 404);
            }

            return $this->success($domain);

        } catch (\Exception $e) {
            Log::error('获取域名详情失败', [
                'domain_id' => $id,
                'error' => $e->getMessage()
            ]);

            return $this->error('获取域名详情失败');
        }
    }

    /**
     * 更新域名
     */
    public function update(Request $request, int $id): JsonResponse
    {
        $request->validate([
            'name' => 'sometimes|string|max:100',
            'domain' => 'sometimes|string|max:255|unique:domain_pools,domain,' . $id,
            'priority' => 'sometimes|integer|min:1|max:100',
            'description' => 'sometimes|string|max:500',
            'status' => 'sometimes|integer|in:1,2,3,4'
        ]);

        try {
            $domain = DomainPool::find($id);
            
            if (!$domain) {
                return $this->error('域名不存在', null, 404);
            }

            $domain->update($request->only(['name', 'domain', 'priority', 'description', 'status']));

            return $this->success($domain, '域名更新成功');

        } catch (\Exception $e) {
            Log::error('更新域名失败', [
                'domain_id' => $id,
                'request_data' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return $this->error('更新域名失败');
        }
    }

    /**
     * 删除域名
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $domain = DomainPool::find($id);
            
            if (!$domain) {
                return $this->error('域名不存在', null, 404);
            }

            $domain->delete();

            return $this->success(null, '域名删除成功');

        } catch (\Exception $e) {
            Log::error('删除域名失败', [
                'domain_id' => $id,
                'error' => $e->getMessage()
            ]);

            return $this->error('删除域名失败');
        }
    }

    /**
     * 批量删除域名
     */
    public function batchDeleteDomains(Request $request): JsonResponse
    {
        $request->validate([
            'ids' => 'required|array|min:1',
            'ids.*' => 'integer|exists:domain_pools,id'
        ]);

        try {
            $deletedCount = DomainPool::whereIn('id', $request->ids)->delete();

            return $this->success([
                'deleted_count' => $deletedCount
            ], "成功删除 {$deletedCount} 个域名");

        } catch (\Exception $e) {
            Log::error('批量删除域名失败', [
                'request_data' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return $this->error('批量删除域名失败');
        }
    }

    // ==================== 短链接管理相关方法 ====================

    /**
     * 获取短链接列表
     */
    public function getShortLinks(Request $request): JsonResponse
    {
        try {
            $query = \App\Models\ShortLink::with('domainPool');

            // 搜索过滤
            if ($request->search) {
                $query->where('short_code', 'like', '%' . $request->search . '%')
                      ->orWhere('original_url', 'like', '%' . $request->search . '%');
            }

            $perPage = $request->get('per_page', 15);
            $shortLinks = $query->orderBy('created_at', 'desc')->paginate($perPage);

            return $this->success($shortLinks);

        } catch (\Exception $e) {
            Log::error('获取短链接列表失败', ['error' => $e->getMessage()]);
            return $this->error('获取短链接列表失败');
        }
    }

    /**
     * 创建短链接
     */
    public function createShortLink(Request $request): JsonResponse
    {
        $request->validate([
            'original_url' => 'required|url',
            'remark' => 'sometimes|string|max:255'
        ]);

        try {
            $shortLink = $this->antiBlockService->createShortLink(
                $request->original_url,
                $request->remark,
                auth()->id()
            );

            if (!$shortLink) {
                return $this->error('创建短链接失败，没有可用的域名');
            }

            return $this->success($shortLink, '短链接创建成功');

        } catch (\Exception $e) {
            Log::error('创建短链接失败', [
                'request_data' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return $this->error('创建短链接失败');
        }
    }

    /**
     * 删除短链接
     */
    public function deleteShortLink(int $id): JsonResponse
    {
        try {
            $shortLink = \App\Models\ShortLink::find($id);
            
            if (!$shortLink) {
                return $this->error('短链接不存在', null, 404);
            }

            $shortLink->delete();

            return $this->success(null, '短链接删除成功');

        } catch (\Exception $e) {
            Log::error('删除短链接失败', [
                'short_link_id' => $id,
                'error' => $e->getMessage()
            ]);

            return $this->error('删除短链接失败');
        }
    }

    // ==================== 统计分析相关方法 ====================

    /**
     * 获取访问统计
     */
    public function getAccessStats(Request $request): JsonResponse
    {
        try {
            // 这里返回模拟数据，实际应该从数据库获取
            $stats = [
                'total_access' => 12580,
                'today_access' => 456,
                'unique_visitors' => 8920,
                'bounce_rate' => 35.6,
                'avg_session_duration' => 180
            ];

            return $this->success($stats);

        } catch (\Exception $e) {
            Log::error('获取访问统计失败', ['error' => $e->getMessage()]);
            return $this->error('获取访问统计失败');
        }
    }

    /**
     * 获取访问日志
     */
    public function getAccessLogs(Request $request): JsonResponse
    {
        try {
            // 这里返回模拟数据，实际应该从数据库获取
            $logs = [
                'data' => [],
                'total' => 0,
                'per_page' => 15,
                'current_page' => 1
            ];

            return $this->success($logs);

        } catch (\Exception $e) {
            Log::error('获取访问日志失败', ['error' => $e->getMessage()]);
            return $this->error('获取访问日志失败');
        }
    }

    /**
     * 获取点击趋势
     */
    public function getClickTrends(Request $request): JsonResponse
    {
        try {
            // 这里返回模拟数据
            $trends = [
                'dates' => ['2024-01-01', '2024-01-02', '2024-01-03', '2024-01-04', '2024-01-05'],
                'clicks' => [120, 150, 180, 200, 165]
            ];

            return $this->success($trends);

        } catch (\Exception $e) {
            Log::error('获取点击趋势失败', ['error' => $e->getMessage()]);
            return $this->error('获取点击趋势失败');
        }
    }

    /**
     * 获取地区统计
     */
    public function getRegionStats(Request $request): JsonResponse
    {
        try {
            // 这里返回模拟数据
            $stats = [
                ['region' => '北京', 'count' => 1250, 'percentage' => 25.5],
                ['region' => '上海', 'count' => 980, 'percentage' => 20.1],
                ['region' => '广州', 'count' => 750, 'percentage' => 15.3],
                ['region' => '深圳', 'count' => 650, 'percentage' => 13.2],
                ['region' => '其他', 'count' => 1270, 'percentage' => 25.9]
            ];

            return $this->success($stats);

        } catch (\Exception $e) {
            Log::error('获取地区统计失败', ['error' => $e->getMessage()]);
            return $this->error('获取地区统计失败');
        }
    }

    /**
     * 获取平台统计
     */
    public function getPlatformStats(Request $request): JsonResponse
    {
        try {
            // 这里返回模拟数据
            $stats = [
                ['platform' => '微信', 'count' => 2580, 'percentage' => 52.3],
                ['platform' => 'QQ', 'count' => 1200, 'percentage' => 24.3],
                ['platform' => '浏览器', 'count' => 890, 'percentage' => 18.1],
                ['platform' => '其他', 'count' => 260, 'percentage' => 5.3]
            ];

            return $this->success($stats);

        } catch (\Exception $e) {
            Log::error('获取平台统计失败', ['error' => $e->getMessage()]);
            return $this->error('获取平台统计失败');
        }
    }

    /**
     * 生成二维码
     */
    public function generateQRCode(Request $request): JsonResponse
    {
        $request->validate([
            'url' => 'required|url',
            'size' => 'sometimes|integer|min:100|max:1000',
            'format' => 'sometimes|in:png,jpg,svg',
            'margin' => 'sometimes|integer|min:0|max:10',
            'error_correction' => 'sometimes|in:L,M,Q,H'
        ]);

        try {
            $size = $request->get('size', 300);
            $format = $request->get('format', 'png');
            $margin = $request->get('margin', 2);
            $errorCorrection = $request->get('error_correction', 'M');
            $url = $request->get('url');

            // 验证URL长度
            if (strlen($url) > 2000) {
                return $this->error('URL长度超出限制', 400);
            }

            // 构建二维码API URL
            $qrCodeUrl = $this->buildQRCodeUrl($url, $size, $format, $margin);

            // 验证生成的二维码是否可访问
            if (!$this->validateQRCodeUrl($qrCodeUrl)) {
                return $this->error('二维码生成服务暂时不可用', 503);
            }

            return $this->success([
                'qr_code_url' => $qrCodeUrl,
                'original_url' => $url,
                'size' => $size,
                'format' => $format,
                'margin' => $margin,
                'error_correction' => $errorCorrection,
                'generated_at' => now()->toISOString()
            ], '二维码生成成功');

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->error('参数验证失败', 422, $e->errors());
        } catch (\Exception $e) {
            Log::error('生成二维码失败', [
                'request_data' => $request->all(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->error('二维码生成失败，请稍后重试', 500);
        }
    }

    /**
     * 构建二维码URL
     */
    private function buildQRCodeUrl(string $url, int $size, string $format, int $margin): string
    {
        $baseUrl = 'https://api.qrserver.com/v1/create-qr-code/';

        $params = [
            'size' => $size . 'x' . $size,
            'data' => $url,
            'format' => $format,
            'margin' => $margin,
            'ecc' => 'M' // 错误纠正级别
        ];

        return $baseUrl . '?' . http_build_query($params);
    }

    /**
     * 验证二维码URL是否可访问
     */
    private function validateQRCodeUrl(string $qrCodeUrl): bool
    {
        try {
            $context = stream_context_create([
                'http' => [
                    'timeout' => 5,
                    'method' => 'HEAD'
                ]
            ]);

            $headers = @get_headers($qrCodeUrl, 1, $context);
            return $headers && strpos($headers[0], '200') !== false;
        } catch (\Exception $e) {
            Log::warning('二维码URL验证失败', ['url' => $qrCodeUrl, 'error' => $e->getMessage()]);
            return false;
        }
    }
}