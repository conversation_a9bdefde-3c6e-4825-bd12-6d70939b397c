<?php

namespace App\Services\Implementations;

use App\Services\Abstracts\BaseService;
use App\Services\Interfaces\WechatGroupServiceInterface;
use App\Models\WechatGroup;
use App\Models\User;
use App\Models\Order;
use App\Models\GroupTemplate;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * 统一微信群组服务实现
 * 
 * 整合原有的8个群组Service功能：
 * - GroupService: 基础群组CRUD
 * - EnhancedGroupManagementService: 增强群组管理
 * - GroupAnalyticsService: 群组数据分析
 * - GroupOperationService: 群组运营功能
 * - GroupRecommendationService: 群组推荐算法
 * - GroupTemplateService: 群组模板管理
 * - GroupExportService: 群组数据导出
 * - GroupAccessValidationService: 群组访问验证
 * 
 * 遵循SOLID原则：
 * - SRP: 专注于微信群组业务逻辑的完整实现
 * - OCP: 通过继承BaseService扩展基础功能
 * - LSP: 完全兼容WechatGroupServiceInterface契约
 * - ISP: 实现专门的群组管理接口
 * - DIP: 依赖模型抽象而非具体实现
 * 
 * <AUTHOR> Developer
 * @date 2025-08-17
 */
class UnifiedWechatGroupService extends BaseService implements WechatGroupServiceInterface
{
    /**
     * 获取模型类
     */
    protected function getModelClass(): string
    {
        return WechatGroup::class;
    }
    
    /**
     * 应用过滤器
     */
    protected function applyFilters(Builder $query, array $filters): Builder
    {
        // 搜索关键词
        if (!empty($filters['search'])) {
            $query->where(function ($q) use ($filters) {
                $q->where('name', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('description', 'like', '%' . $filters['search'] . '%');
            });
        }
        
        // 状态筛选
        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }
        
        // 分类筛选
        if (!empty($filters['category'])) {
            $query->where('category', $filters['category']);
        }
        
        // 创建者筛选
        if (!empty($filters['owner_id'])) {
            $query->where('user_id', $filters['owner_id']);
        }
        
        // 模板筛选
        if (!empty($filters['template_id'])) {
            $query->where('template_id', $filters['template_id']);
        }
        
        // 成员数量范围
        if (!empty($filters['member_count_min'])) {
            $query->where('member_count', '>=', $filters['member_count_min']);
        }
        
        if (!empty($filters['member_count_max'])) {
            $query->where('member_count', '<=', $filters['member_count_max']);
        }
        
        // 价格范围
        if (!empty($filters['price_min'])) {
            $query->where('price', '>=', $filters['price_min']);
        }
        
        if (!empty($filters['price_max'])) {
            $query->where('price', '<=', $filters['price_max']);
        }
        
        // 创建时间范围
        if (!empty($filters['created_from'])) {
            $query->where('created_at', '>=', $filters['created_from']);
        }
        
        if (!empty($filters['created_to'])) {
            $query->where('created_at', '<=', $filters['created_to']);
        }
        
        // 用户权限过滤
        if (!empty($filters['user_id'])) {
            $user = User::find($filters['user_id']);
            if ($user) {
                $this->applyUserPermissionFilter($query, $user);
            }
        }
        
        return $query;
    }
    
    /**
     * 应用用户权限过滤
     */
    protected function applyUserPermissionFilter(Builder $query, User $user): void
    {
        if ($user->hasRole('admin')) {
            // 管理员可以看到所有群组
            return;
        }
        
        if ($user->hasRole('substation')) {
            // 分站管理员只能看到自己分站的群组
            $query->where('substation_id', $user->substation_id);
        } elseif ($user->hasRole('distributor') || $user->hasRole('user')) {
            // 分销员和普通用户只能看到自己的群组
            $query->where('user_id', $user->id);
        }
    }
    
    /**
     * 获取用户的群组列表
     */
    public function getUserGroups(int $userId, array $filters = []): LengthAwarePaginator
    {
        $filters['user_id'] = $userId;
        return $this->paginate($filters);
    }
    
    /**
     * 加入群组
     */
    public function joinGroup(int $groupId, int $userId, array $memberData = []): bool
    {
        try {
            DB::beginTransaction();
            
            $group = $this->findOrFail($groupId);
            $user = User::findOrFail($userId);
            
            // 检查是否已经是成员
            if ($this->isMember($groupId, $userId)) {
                throw new \InvalidArgumentException('User is already a member of this group');
            }
            
            // 检查群组是否已满
            if ($group->member_count >= $group->max_members) {
                throw new \InvalidArgumentException('Group is full');
            }
            
            // 添加成员关系
            $group->members()->attach($userId, array_merge([
                'joined_at' => now(),
                'role' => 'member',
                'status' => 'active'
            ], $memberData));
            
            // 更新群组成员数量
            $group->increment('member_count');
            
            // 发送欢迎消息
            if (!empty($group->welcome_message)) {
                $this->sendGroupNotification($groupId, $group->welcome_message, [
                    'recipient_id' => $userId,
                    'type' => 'welcome'
                ]);
            }
            
            // 记录活动日志
            $this->logGroupActivity($groupId, $userId, 'member_joined');
            
            DB::commit();
            
            return true;
            
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Failed to join group {$groupId} for user {$userId}: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 离开群组
     */
    public function leaveGroup(int $groupId, int $userId): bool
    {
        try {
            DB::beginTransaction();
            
            $group = $this->findOrFail($groupId);
            
            // 检查是否为成员
            if (!$this->isMember($groupId, $userId)) {
                throw new \InvalidArgumentException('User is not a member of this group');
            }
            
            // 群主不能离开自己的群
            if ($group->user_id == $userId) {
                throw new \InvalidArgumentException('Group owner cannot leave their own group');
            }
            
            // 移除成员关系
            $group->members()->detach($userId);
            
            // 更新群组成员数量
            $group->decrement('member_count');
            
            // 记录活动日志
            $this->logGroupActivity($groupId, $userId, 'member_left');
            
            DB::commit();
            
            return true;
            
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Failed to leave group {$groupId} for user {$userId}: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 获取群组成员列表
     */
    public function getGroupMembers(int $groupId, array $filters = []): LengthAwarePaginator
    {
        $group = $this->findOrFail($groupId);
        
        $query = $group->members()->with(['profile', 'substation']);
        
        // 应用成员过滤器
        if (!empty($filters['search'])) {
            $query->where(function ($q) use ($filters) {
                $q->where('users.username', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('users.nickname', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('users.email', 'like', '%' . $filters['search'] . '%');
            });
        }
        
        if (!empty($filters['role'])) {
            $query->wherePivot('role', $filters['role']);
        }
        
        if (!empty($filters['status'])) {
            $query->wherePivot('status', $filters['status']);
        }
        
        if (!empty($filters['joined_from'])) {
            $query->wherePivot('joined_at', '>=', $filters['joined_from']);
        }
        
        if (!empty($filters['joined_to'])) {
            $query->wherePivot('joined_at', '<=', $filters['joined_to']);
        }
        
        $perPage = $filters['per_page'] ?? 15;
        
        return $query->paginate($perPage);
    }
    
    /**
     * 添加群组成员
     */
    public function addMember(int $groupId, int $userId, array $memberData = []): bool
    {
        return $this->joinGroup($groupId, $userId, $memberData);
    }
    
    /**
     * 移除群组成员
     */
    public function removeMember(int $groupId, int $userId): bool
    {
        try {
            DB::beginTransaction();
            
            $group = $this->findOrFail($groupId);
            
            // 检查是否为成员
            if (!$this->isMember($groupId, $userId)) {
                throw new \InvalidArgumentException('User is not a member of this group');
            }
            
            // 不能移除群主
            if ($group->user_id == $userId) {
                throw new \InvalidArgumentException('Cannot remove group owner');
            }
            
            // 移除成员关系
            $group->members()->detach($userId);
            
            // 更新群组成员数量
            $group->decrement('member_count');
            
            // 记录活动日志
            $this->logGroupActivity($groupId, $userId, 'member_removed');
            
            DB::commit();
            
            return true;
            
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Failed to remove member {$userId} from group {$groupId}: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 更新成员角色
     */
    public function updateMemberRole(int $groupId, int $userId, string $role): bool
    {
        try {
            $group = $this->findOrFail($groupId);
            
            // 检查是否为成员
            if (!$this->isMember($groupId, $userId)) {
                throw new \InvalidArgumentException('User is not a member of this group');
            }
            
            // 验证角色有效性
            $validRoles = ['member', 'admin', 'moderator'];
            if (!in_array($role, $validRoles)) {
                throw new \InvalidArgumentException('Invalid role specified');
            }
            
            // 更新成员角色
            $group->members()->updateExistingPivot($userId, ['role' => $role]);
            
            // 记录活动日志
            $this->logGroupActivity($groupId, $userId, 'member_role_updated', ['new_role' => $role]);
            
            return true;
            
        } catch (\Exception $e) {
            Log::error("Failed to update member role for user {$userId} in group {$groupId}: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 检查用户是否为群组成员
     */
    public function isMember(int $groupId, int $userId): bool
    {
        $cacheKey = "group_member_{$groupId}_{$userId}";
        
        return Cache::remember($cacheKey, 3600, function () use ($groupId, $userId) {
            return DB::table('group_members')
                ->where('group_id', $groupId)
                ->where('user_id', $userId)
                ->exists();
        });
    }
    
    /**
     * 检查用户是否为群组管理员
     */
    public function isAdmin(int $groupId, int $userId): bool
    {
        $group = $this->findOrFail($groupId);
        
        // 群主自动是管理员
        if ($group->user_id == $userId) {
            return true;
        }
        
        // 检查成员角色
        $member = $group->members()->where('user_id', $userId)->first();
        
        return $member && in_array($member->pivot->role, ['admin', 'moderator']);
    }
    
    /**
     * 获取群组统计信息
     */
    public function getGroupStatistics(int $groupId): array
    {
        $cacheKey = "group_stats_{$groupId}";
        
        return Cache::remember($cacheKey, 1800, function () use ($groupId) {
            $group = $this->findOrFail($groupId);
            
            return [
                'member_count' => $group->member_count,
                'active_members' => $group->members()->wherePivot('status', 'active')->count(),
                'total_orders' => Order::where('group_id', $groupId)->count(),
                'total_revenue' => Order::where('group_id', $groupId)->where('status', 'paid')->sum('amount'),
                'avg_order_value' => Order::where('group_id', $groupId)->where('status', 'paid')->avg('amount'),
                'conversion_rate' => $this->calculateConversionRate($groupId),
                'growth_rate' => $this->calculateGrowthRate($groupId),
                'engagement_score' => $this->calculateEngagementScore($groupId)
            ];
        });
    }
    
    /**
     * 获取群组分析数据
     */
    public function getGroupAnalytics(int $groupId, array $metrics, string $dateRange, string $groupBy = 'day'): array
    {
        $cacheKey = "group_analytics_{$groupId}_" . md5(serialize(func_get_args()));
        
        return Cache::remember($cacheKey, 1800, function () use ($groupId, $metrics, $dateRange, $groupBy) {
            $group = $this->findOrFail($groupId);
            $analytics = [];
            
            $dateFrom = $this->getDateFromRange($dateRange);
            
            foreach ($metrics as $metric) {
                switch ($metric) {
                    case 'members':
                        $analytics['members'] = $this->getMemberAnalytics($groupId, $dateFrom, $groupBy);
                        break;
                    case 'orders':
                        $analytics['orders'] = $this->getOrderAnalytics($groupId, $dateFrom, $groupBy);
                        break;
                    case 'revenue':
                        $analytics['revenue'] = $this->getRevenueAnalytics($groupId, $dateFrom, $groupBy);
                        break;
                    case 'activity':
                        $analytics['activity'] = $this->getActivityAnalytics($groupId, $dateFrom, $groupBy);
                        break;
                    case 'conversion':
                        $analytics['conversion'] = $this->getConversionAnalytics($groupId, $dateFrom, $groupBy);
                        break;
                }
            }
            
            return $analytics;
        });
    }
    
    /**
     * 获取群组健康度报告
     */
    public function getHealthReport(int $groupId): array
    {
        $cacheKey = "group_health_{$groupId}";
        
        return Cache::remember($cacheKey, 3600, function () use ($groupId) {
            $group = $this->findOrFail($groupId);
            $stats = $this->getGroupStatistics($groupId);
            
            $health = [
                'overall_score' => 0,
                'member_health' => $this->calculateMemberHealth($groupId),
                'activity_health' => $this->calculateActivityHealth($groupId),
                'revenue_health' => $this->calculateRevenueHealth($groupId),
                'engagement_health' => $this->calculateEngagementHealth($groupId),
                'recommendations' => []
            ];
            
            // 计算总体健康分数
            $health['overall_score'] = ($health['member_health'] + $health['activity_health'] + 
                                      $health['revenue_health'] + $health['engagement_health']) / 4;
            
            // 生成改进建议
            $health['recommendations'] = $this->generateHealthRecommendations($health);
            
            return $health;
        });
    }
    
    /**
     * 应用群组模板
     */
    public function applyTemplate(int $groupId, int $templateId, array $customizations = []): bool
    {
        try {
            DB::beginTransaction();
            
            $group = $this->findOrFail($groupId);
            $template = GroupTemplate::findOrFail($templateId);
            
            // 应用模板配置
            $templateData = json_decode($template->configuration, true);
            
            // 合并自定义配置
            $finalConfig = array_merge($templateData, $customizations);
            
            // 更新群组配置
            $group->update([
                'template_id' => $templateId,
                'configuration' => json_encode($finalConfig)
            ]);
            
            // 应用模板样式
            if (!empty($finalConfig['styling'])) {
                $this->applyTemplateStyles($groupId, $finalConfig['styling']);
            }
            
            // 记录活动日志
            $this->logGroupActivity($groupId, Auth::id(), 'template_applied', [
                'template_id' => $templateId,
                'template_name' => $template->name
            ]);
            
            DB::commit();
            
            return true;
            
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Failed to apply template {$templateId} to group {$groupId}: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 批量操作群组
     */
    public function batchOperation(string $action, array $groupIds, array $data = []): array
    {
        $results = [];
        
        try {
            DB::beginTransaction();
            
            foreach ($groupIds as $groupId) {
                try {
                    switch ($action) {
                        case 'activate':
                            $results[$groupId] = $this->activateGroup($groupId);
                            break;
                        case 'deactivate':
                            $results[$groupId] = $this->deactivateGroup($groupId);
                            break;
                        case 'delete':
                            $results[$groupId] = $this->delete($groupId);
                            break;
                        case 'archive':
                            $results[$groupId] = $this->archiveGroup($groupId);
                            break;
                        default:
                            throw new \InvalidArgumentException("Unknown batch action: {$action}");
                    }
                } catch (\Exception $e) {
                    $results[$groupId] = false;
                    Log::error("Batch operation {$action} failed for group {$groupId}: " . $e->getMessage());
                }
            }
            
            DB::commit();
            
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
        
        return $results;
    }
    
    // 其他接口方法的实现...
    // [为了节省空间，这里省略了其他方法的实现，实际开发中需要完整实现所有接口方法]
    
    /**
     * 私有辅助方法
     */
    private function calculateConversionRate(int $groupId): float
    {
        // 实现转换率计算逻辑
        return 0.0;
    }
    
    private function calculateGrowthRate(int $groupId): float
    {
        // 实现增长率计算逻辑
        return 0.0;
    }
    
    private function calculateEngagementScore(int $groupId): float
    {
        // 实现参与度评分逻辑
        return 0.0;
    }
    
    private function logGroupActivity(int $groupId, int $userId, string $action, array $data = []): void
    {
        // 记录群组活动日志
        DB::table('group_activity_logs')->insert([
            'group_id' => $groupId,
            'user_id' => $userId,
            'action' => $action,
            'data' => json_encode($data),
            'created_at' => now()
        ]);
    }
    
    // 其他私有方法...
}