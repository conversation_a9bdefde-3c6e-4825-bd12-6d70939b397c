/**
 * 统一的二维码生成工具
 * 提供多种生成方式和错误处理
 */

import QRCode from 'qrcode'
import { ElMessage } from 'element-plus'

/**
 * 二维码生成配置
 */
export const QR_CONFIG = {
  // 默认配置
  DEFAULT: {
    width: 300,
    height: 300,
    margin: 2,
    errorCorrectionLevel: 'M',
    type: 'image/png',
    color: {
      dark: '#000000',
      light: '#FFFFFF'
    }
  },
  
  // 小尺寸配置
  SMALL: {
    width: 150,
    height: 150,
    margin: 1,
    errorCorrectionLevel: 'M'
  },
  
  // 大尺寸配置
  LARGE: {
    width: 500,
    height: 500,
    margin: 4,
    errorCorrectionLevel: 'H'
  }
}

/**
 * 二维码生成服务类
 */
export class QRCodeService {
  /**
   * 生成二维码（主要方法）
   * @param {string} text - 要生成二维码的文本
   * @param {Object} options - 生成选项
   * @returns {Promise<string>} 二维码DataURL
   */
  static async generate(text, options = {}) {
    if (!text) {
      throw new Error('请提供要生成二维码的内容')
    }

    const config = { ...QR_CONFIG.DEFAULT, ...options }
    
    try {
      // 方法1：使用本地qrcode库生成
      return await this.generateLocal(text, config)
    } catch (error) {
      console.warn('本地生成失败，尝试备用方案:', error)
      
      try {
        // 方法2：使用在线API生成
        return await this.generateOnline(text, config)
      } catch (fallbackError) {
        console.error('所有生成方法都失败:', fallbackError)
        throw new Error('二维码生成失败，请检查网络连接或稍后重试')
      }
    }
  }

  /**
   * 本地生成二维码
   * @param {string} text - 文本内容
   * @param {Object} config - 配置选项
   * @returns {Promise<string>} DataURL
   */
  static async generateLocal(text, config) {
    return await QRCode.toDataURL(text, {
      width: config.width,
      height: config.height,
      margin: config.margin,
      errorCorrectionLevel: config.errorCorrectionLevel,
      color: config.color
    })
  }

  /**
   * 在线API生成二维码
   * @param {string} text - 文本内容
   * @param {Object} config - 配置选项
   * @returns {Promise<string>} 图片URL
   */
  static async generateOnline(text, config) {
    const size = config.width || 300
    const url = `https://api.qrserver.com/v1/create-qr-code/?size=${size}x${size}&data=${encodeURIComponent(text)}`
    
    // 验证URL是否可访问
    return new Promise((resolve, reject) => {
      const img = new Image()
      img.onload = () => resolve(url)
      img.onerror = () => reject(new Error('在线API不可用'))
      img.src = url
    })
  }

  /**
   * 生成二维码到Canvas
   * @param {HTMLCanvasElement} canvas - Canvas元素
   * @param {string} text - 文本内容
   * @param {Object} options - 选项
   */
  static async generateToCanvas(canvas, text, options = {}) {
    const config = { ...QR_CONFIG.DEFAULT, ...options }
    
    try {
      await QRCode.toCanvas(canvas, text, config)
      return canvas.toDataURL('image/png')
    } catch (error) {
      console.error('Canvas生成失败:', error)
      throw error
    }
  }

  /**
   * 批量生成二维码
   * @param {Array} textList - 文本列表
   * @param {Object} options - 选项
   * @returns {Promise<Array>} 二维码URL列表
   */
  static async generateBatch(textList, options = {}) {
    const results = []
    
    for (const text of textList) {
      try {
        const qrCode = await this.generate(text, options)
        results.push({ text, qrCode, success: true })
      } catch (error) {
        results.push({ text, error: error.message, success: false })
      }
    }
    
    return results
  }

  /**
   * 下载二维码
   * @param {string} dataUrl - 二维码DataURL
   * @param {string} filename - 文件名
   */
  static downloadQRCode(dataUrl, filename = '二维码.png') {
    try {
      const link = document.createElement('a')
      link.href = dataUrl
      link.download = filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      ElMessage.success('二维码下载成功')
    } catch (error) {
      console.error('下载失败:', error)
      ElMessage.error('下载失败')
    }
  }

  /**
   * 复制二维码到剪贴板
   * @param {string} dataUrl - 二维码DataURL
   */
  static async copyQRCode(dataUrl) {
    try {
      // 将DataURL转换为Blob
      const response = await fetch(dataUrl)
      const blob = await response.blob()
      
      // 复制到剪贴板
      await navigator.clipboard.write([
        new ClipboardItem({ 'image/png': blob })
      ])
      
      ElMessage.success('二维码已复制到剪贴板')
    } catch (error) {
      console.error('复制失败:', error)
      ElMessage.error('复制失败，请手动保存')
    }
  }

  /**
   * 验证文本是否适合生成二维码
   * @param {string} text - 文本内容
   * @returns {boolean} 是否有效
   */
  static validateText(text) {
    if (!text || typeof text !== 'string') {
      return false
    }
    
    // 检查长度限制（二维码有数据容量限制）
    if (text.length > 2000) {
      return false
    }
    
    return true
  }

  /**
   * 获取推荐的纠错级别
   * @param {string} text - 文本内容
   * @returns {string} 纠错级别
   */
  static getRecommendedErrorCorrection(text) {
    if (text.length < 100) return 'H' // 高纠错
    if (text.length < 500) return 'M' // 中等纠错
    return 'L' // 低纠错
  }
}

/**
 * 便捷方法：快速生成二维码
 * @param {string} text - 文本内容
 * @param {Object} options - 选项
 * @returns {Promise<string>} 二维码DataURL
 */
export const generateQRCode = (text, options = {}) => {
  return QRCodeService.generate(text, options)
}

/**
 * 便捷方法：生成推广链接二维码
 * @param {string} url - 推广链接
 * @param {Object} options - 选项
 * @returns {Promise<string>} 二维码DataURL
 */
export const generatePromotionQRCode = (url, options = {}) => {
  const config = {
    ...QR_CONFIG.DEFAULT,
    errorCorrectionLevel: 'M',
    ...options
  }
  
  return QRCodeService.generate(url, config)
}

export default QRCodeService
