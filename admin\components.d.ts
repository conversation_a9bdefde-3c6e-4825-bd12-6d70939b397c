/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    ActivityHeatmap: typeof import('./src/components/dashboard/ActivityHeatmap.vue')['default']
    AgentApplications: typeof import('./src/views/agent/AgentApplications.vue')['default']
    AgentCommission: typeof import('./src/views/agent/AgentCommission.vue')['default']
    AgentDashboard: typeof import('./src/views/agent/AgentDashboard.vue')['default']
    'AgentDashboard.test': typeof import('./src/views/agent/AgentDashboard.test.vue')['default']
    AgentDashboardFixed: typeof import('./src/views/agent/AgentDashboardFixed.vue')['default']
    AgentDashboardSimple: typeof import('./src/views/agent/AgentDashboardSimple.vue')['default']
    AgentHierarchy: typeof import('./src/views/agent/AgentHierarchy.vue')['default']
    AgentHierarchyOptimized: typeof import('./src/views/agent/AgentHierarchyOptimized.vue')['default']
    AgentHierarchySimple: typeof import('./src/views/agent/AgentHierarchySimple.vue')['default']
    AgentList: typeof import('./src/views/agent/AgentList.vue')['default']
    AgentListAntd: typeof import('./src/views/agent/AgentListAntd.vue')['default']
    AgentPerformance: typeof import('./src/views/agent/AgentPerformance.vue')['default']
    AgentPermissionMatrix: typeof import('./src/views/system/components/AgentPermissionMatrix.vue')['default']
    AIContentGenerator: typeof import('./src/components/AIContentGenerator.vue')['default']
    AIContentTest: typeof import('./src/views/test/AIContentTest.vue')['default']
    AIEnhancedSearch: typeof import('./src/components/AIEnhancedSearch.vue')['default']
    Analytics: typeof import('./src/views/anti-block/Analytics.vue')['default']
    AnalyticsDashboard: typeof import('./src/views/community/AnalyticsDashboard.vue')['default']
    AntiBlockRealTimeMonitor: typeof import('./src/components/AntiBlockRealTimeMonitor.vue')['default']
    APITest: typeof import('./src/views/test/APITest.vue')['default']
    AuditLogs: typeof import('./src/views/system/components/AuditLogs.vue')['default']
    AutoRules: typeof import('./src/views/community/AutoRules.vue')['default']
    AvatarEditDialog: typeof import('./src/views/system/components/AvatarEditDialog.vue')['default']
    AvatarLibrarySelector: typeof import('./src/components/AvatarLibrarySelector.vue')['default']
    AvatarLibraryTest: typeof import('./src/views/test/AvatarLibraryTest.vue')['default']
    AvatarManagement: typeof import('./src/views/system/AvatarManagement.vue')['default']
    AvatarPreviewDialog: typeof import('./src/views/system/components/AvatarPreviewDialog.vue')['default']
    AvatarUpload: typeof import('./src/components/AvatarUpload.vue')['default']
    AvatarUploadDialog: typeof import('./src/views/system/components/AvatarUploadDialog.vue')['default']
    BackupSettings: typeof import('./src/views/system/components/BackupSettings.vue')['default']
    BalanceDialog: typeof import('./src/views/user/components/BalanceDialog.vue')['default']
    BannerPreview: typeof import('./src/components/preview/BannerPreview.vue')['default']
    BarChart: typeof import('./src/components/Charts/BarChart.vue')['default']
    BaseButton: typeof import('./src/components/base/BaseButton.vue')['default']
    BaseChart: typeof import('./src/components/base/BaseChart.vue')['default']
    BaseComponentExample: typeof import('./src/components/examples/BaseComponentExample.vue')['default']
    BaseDialog: typeof import('./src/components/base/BaseDialog.vue')['default']
    BaseForm: typeof import('./src/components/base/BaseForm.vue')['default']
    BaseTable: typeof import('./src/components/base/BaseTable.vue')['default']
    BasicTest: typeof import('./src/views/test/BasicTest.vue')['default']
    BatchActionDialog: typeof import('./src/views/promotion/components/BatchActionDialog.vue')['default']
    BatchAnalysisDialog: typeof import('./src/views/community/components/BatchAnalysisDialog.vue')['default']
    Bookmarks: typeof import('./src/components/navigation/Bookmarks.vue')['default']
    Breadcrumb: typeof import('./src/components/navigation/Breadcrumb.vue')['default']
    CampaignManagement: typeof import('./src/views/promotion/CampaignManagement.vue')['default']
    ChinaMapChart: typeof import('./src/components/Charts/ChinaMapChart.vue')['default']
    CircularProgress: typeof import('./src/components/UI/CircularProgress.vue')['default']
    CityPlaceholderTest: typeof import('./src/views/test/CityPlaceholderTest.vue')['default']
    CollapsedTooltips: typeof import('./src/components/navigation/components/CollapsedTooltips.vue')['default']
    CollapseTransition: typeof import('./src/components/transitions/CollapseTransition.vue')['default']
    ColumnSettings: typeof import('./src/views/community/components/ColumnSettings.vue')['default']
    CommissionLog: typeof import('./src/views/finance/CommissionLog.vue')['default']
    CommissionLogs: typeof import('./src/views/distributor/CommissionLogs.vue')['default']
    CommunitySettings: typeof import('./src/views/system/components/CommunitySettings.vue')['default']
    CompleteCampaignManagement: typeof import('./src/views/promotion/CompleteCampaignManagement.vue')['default']
    CompleteLinkManagement: typeof import('./src/views/promotion/CompleteLinkManagement.vue')['default']
    CompleteSubstationAnalytics: typeof import('./src/views/substation/CompleteSubstationAnalytics.vue')['default']
    CompleteSubstationFinance: typeof import('./src/views/substation/CompleteSubstationFinance.vue')['default']
    ComponentDemo: typeof import('./src/views/demo/ComponentDemo.vue')['default']
    ComponentImportTest: typeof import('./src/views/test/ComponentImportTest.vue')['default']
    ComponentProperties: typeof import('./src/views/promotion/components/ComponentProperties.vue')['default']
    ComprehensiveDashboard: typeof import('./src/views/analytics/ComprehensiveDashboard.vue')['default']
    ContentCard: typeof import('./src/views/community/components/ContentCard.vue')['default']
    ContentDetailDialog: typeof import('./src/views/community/components/ContentDetailDialog.vue')['default']
    ContentModeration: typeof import('./src/views/community/ContentModeration.vue')['default']
    ContentModerationEnhanced: typeof import('./src/views/community/components/ContentModerationEnhanced.vue')['default']
    ContentPreview: typeof import('./src/components/preview/ContentPreview.vue')['default']
    ContentPreviewDialog: typeof import('./src/views/community/components/ContentPreviewDialog.vue')['default']
    ContentQualityAnalyzer: typeof import('./src/components/ContentQualityAnalyzer.vue')['default']
    ContentTemplateLibrary: typeof import('./src/components/ContentTemplateLibrary.vue')['default']
    ConversionOptimizer: typeof import('./src/components/ConversionOptimizer.vue')['default']
    CountTo: typeof import('./src/components/CountTo.vue')['default']
    CustomerDetail: typeof import('./src/views/distributor/components/CustomerDetail.vue')['default']
    CustomerDialog: typeof import('./src/views/distributor/components/CustomerDialog.vue')['default']
    CustomerManagement: typeof import('./src/views/distributor/CustomerManagement.vue')['default']
    Dashboard: typeof import('./src/views/anti-block/Dashboard.vue')['default']
    DashboardActivities: typeof import('./src/components/dashboard/DashboardActivities.vue')['default']
    DashboardCard: typeof import('./src/components/dashboard/DashboardCard.vue')['default']
    DashboardCharts: typeof import('./src/components/dashboard/DashboardCharts.vue')['default']
    DashboardMetrics: typeof import('./src/components/dashboard/DashboardMetrics.vue')['default']
    DataExport: typeof import('./src/views/system/DataExport.vue')['default']
    DataScreen: typeof import('./src/views/dashboard/DataScreen.vue')['default']
    DataScreenDemo: typeof import('./src/views/dashboard/DataScreenDemo.vue')['default']
    DataScreenFullscreen: typeof import('./src/views/dashboard/DataScreenFullscreen.vue')['default']
    DataScreenRouter: typeof import('./src/views/dashboard/DataScreenRouter.vue')['default']
    DataScreenTest: typeof import('./src/views/dev-tools/DataScreenTest.vue')['default']
    DataTable: typeof import('./src/components/common/DataTable.vue')['default']
    DevTools: typeof import('./src/components/DevTools.vue')['default']
    DiagnosticPage: typeof import('./src/views/debug/DiagnosticPage.vue')['default']
    DistributorDashboard: typeof import('./src/views/distributor/DistributorDashboard.vue')['default']
    DistributorDetail: typeof import('./src/views/distribution/DistributorDetail.vue')['default']
    DistributorList: typeof import('./src/views/distribution/DistributorList.vue')['default']
    DistributorListAntd: typeof import('./src/views/distribution/DistributorListAntd.vue')['default']
    DomainList: typeof import('./src/views/anti-block/DomainList.vue')['default']
    DomainListAntd: typeof import('./src/views/anti-block/DomainListAntd.vue')['default']
    DomainNavigation: typeof import('./src/components/navigation/DomainNavigation.vue')['default']
    DomainNavigationV2: typeof import('./src/components/navigation/DomainNavigationV2.vue')['default']
    DomainSection: typeof import('./src/components/navigation/components/DomainSection.vue')['default']
    DoughnutChart: typeof import('./src/components/Charts/DoughnutChart.vue')['default']
    DragDropEditor: typeof import('./src/components/DragDropEditor.vue')['default']
    DynamicForm: typeof import('./src/components/common/DynamicForm.vue')['default']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElBadge: typeof import('element-plus/es')['ElBadge']
    ElBreadcrumb: typeof import('element-plus/es')['ElBreadcrumb']
    ElBreadcrumbItem: typeof import('element-plus/es')['ElBreadcrumbItem']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElButtonGroup: typeof import('element-plus/es')['ElButtonGroup']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCascader: typeof import('element-plus/es')['ElCascader']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElCollapse: typeof import('element-plus/es')['ElCollapse']
    ElCollapseItem: typeof import('element-plus/es')['ElCollapseItem']
    ElColorPicker: typeof import('element-plus/es')['ElColorPicker']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioButton: typeof import('element-plus/es')['ElRadioButton']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRate: typeof import('element-plus/es')['ElRate']
    ElResult: typeof import('element-plus/es')['ElResult']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSkeletonItem: typeof import('element-plus/es')['ElSkeletonItem']
    ElSlider: typeof import('element-plus/es')['ElSlider']
    ElSpace: typeof import('element-plus/es')['ElSpace']
    ElStatistic: typeof import('element-plus/es')['ElStatistic']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElText: typeof import('element-plus/es')['ElText']
    ElTimeline: typeof import('element-plus/es')['ElTimeline']
    ElTimelineItem: typeof import('element-plus/es')['ElTimelineItem']
    ElTimePicker: typeof import('element-plus/es')['ElTimePicker']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElTree: typeof import('element-plus/es')['ElTree']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    Enhanced404: typeof import('./src/components/Enhanced404.vue')['default']
    EnhancedAIContentGenerator: typeof import('./src/components/EnhancedAIContentGenerator.vue')['default']
    EnhancedAvatarLibrarySelector: typeof import('./src/components/EnhancedAvatarLibrarySelector.vue')['default']
    EnhancedDashboard: typeof import('./src/views/anti-block/EnhancedDashboard.vue')['default']
    EnhancedDataScreen: typeof import('./src/views/dashboard/EnhancedDataScreen.vue')['default']
    EnhancedGlobalSearch: typeof import('./src/components/EnhancedGlobalSearch.vue')['default']
    EnhancedGlobalSearchV2: typeof import('./src/components/EnhancedGlobalSearchV2.vue')['default']
    EnhancedGroupTemplateSelector: typeof import('./src/components/EnhancedGroupTemplateSelector.vue')['default']
    EnhancedImageSelector: typeof import('./src/components/EnhancedImageSelector.vue')['default']
    EnhancedNavigationSystem: typeof import('./src/components/navigation/EnhancedNavigationSystem.vue')['default']
    EnhancedOptimizedSidebar: typeof import('./src/components/layout/EnhancedOptimizedSidebar.vue')['default']
    EnhancedPermissionDialog: typeof import('./src/views/permission/components/EnhancedPermissionDialog.vue')['default']
    EnhancedPermissionManagement: typeof import('./src/views/system/components/EnhancedPermissionManagement.vue')['default']
    EnhancedSidebarItem: typeof import('./src/components/layout/EnhancedSidebarItem.vue')['default']
    EnhancedStatCard: typeof import('./src/components/dashboard/EnhancedStatCard.vue')['default']
    ErrorBoundary: typeof import('./src/components/ErrorBoundary.vue')['default']
    ErrorPage: typeof import('./src/views/ErrorPage.vue')['default']
    EventEditDialog: typeof import('./src/views/community/components/EventEditDialog.vue')['default']
    EventManagement: typeof import('./src/views/community/EventManagement.vue')['default']
    FeaturesComponent: typeof import('./src/views/promotion/components/components/FeaturesComponent.vue')['default']
    FileManagement: typeof import('./src/views/system/FileManagement.vue')['default']
    FileUploader: typeof import('./src/components/FileUploader.vue')['default']
    FinanceAnalytics: typeof import('./src/views/analytics/FinanceAnalytics.vue')['default']
    FinanceDashboard: typeof import('./src/views/finance/FinanceDashboard.vue')['default']
    FollowUpDialog: typeof import('./src/views/distributor/components/FollowUpDialog.vue')['default']
    FooterComponent: typeof import('./src/views/promotion/components/components/FooterComponent.vue')['default']
    FormComponent: typeof import('./src/views/promotion/components/components/FormComponent.vue')['default']
    FunctionTest: typeof import('./src/views/system/FunctionTest.vue')['default']
    FunnelChart: typeof import('./src/components/Charts/FunnelChart.vue')['default']
    GalleryPreview: typeof import('./src/components/preview/GalleryPreview.vue')['default']
    GlobalSearch: typeof import('./src/components/navigation/GlobalSearch.vue')['default']
    GroupActivityWidget: typeof import('./src/components/widgets/GroupActivityWidget.vue')['default']
    GroupAddEnhanced: typeof import('./src/views/community/GroupAddEnhanced.vue')['default']
    GroupAddEnhancedTest: typeof import('./src/views/dev-tools/GroupAddEnhancedTest.vue')['default']
    GroupAddTest: typeof import('./src/views/dev-tools/GroupAddTest.vue')['default']
    GroupAnalytics: typeof import('./src/views/community/components/GroupAnalytics.vue')['default']
    GroupAnalyticsAdvanced: typeof import('./src/views/community/components/GroupAnalyticsAdvanced.vue')['default']
    GroupAnalyticsSimple: typeof import('./src/views/community/components/GroupAnalyticsSimple.vue')['default']
    GroupCard: typeof import('./src/views/community/components/GroupCard.vue')['default']
    GroupContentManager: typeof import('./src/views/community/components/GroupContentManager.vue')['default']
    GroupCreateComplete: typeof import('./src/views/community/components/GroupCreateComplete.vue')['default']
    GroupCreateForm: typeof import('./src/components/GroupCreateForm.vue')['default']
    GroupCreateInline: typeof import('./src/views/community/components/GroupCreateInline.vue')['default']
    GroupCreateSimple: typeof import('./src/views/community/components/GroupCreateSimple.vue')['default']
    GroupCreateSteps: typeof import('./src/components/GroupCreateSteps.vue')['default']
    GroupDetail: typeof import('./src/views/community/GroupDetail.vue')['default']
    GroupDetailDrawer: typeof import('./src/views/community/components/GroupDetailDrawer.vue')['default']
    GroupDialog: typeof import('./src/views/community/components/GroupDialog.vue')['default']
    GroupDialog_broken: typeof import('./src/views/community/components/GroupDialog_broken.vue')['default']
    GroupDialog_fixed: typeof import('./src/views/community/components/GroupDialog_fixed.vue')['default']
    GroupDialog_new: typeof import('./src/views/community/components/GroupDialog_new.vue')['default']
    GroupDialog_temp: typeof import('./src/views/community/components/GroupDialog_temp.vue')['default']
    GroupDialogEnhanced: typeof import('./src/views/community/components/GroupDialogEnhanced.vue')['default']
    GroupDialogSimple: typeof import('./src/views/community/components/GroupDialogSimple.vue')['default']
    GroupFormUnified: typeof import('./src/components/GroupFormUnified.vue')['default']
    GroupKanban: typeof import('./src/views/community/components/GroupKanban.vue')['default']
    GroupLandingPreview: typeof import('./src/components/GroupLandingPreview.vue')['default']
    GroupList: typeof import('./src/views/community/GroupList.vue')['default']
    GroupListEnhanced: typeof import('./src/views/community/components/GroupListEnhanced.vue')['default']
    GroupManagement: typeof import('./src/views/distributor/GroupManagement.vue')['default']
    GroupMarketing: typeof import('./src/views/community/GroupMarketing.vue')['default']
    GroupMemberManager: typeof import('./src/views/community/components/GroupMemberManager.vue')['default']
    GroupOwnerPermissionManager: typeof import('./src/views/system/components/GroupOwnerPermissionManager.vue')['default']
    GroupQuickInfo: typeof import('./src/views/community/components/GroupQuickInfo.vue')['default']
    GroupSettings: typeof import('./src/views/community/GroupSettings.vue')['default']
    GroupTemplateSelector: typeof import('./src/components/GroupTemplateSelector.vue')['default']
    GuideCard: typeof import('./src/components/UI/GuideCard.vue')['default']
    HeaderComponent: typeof import('./src/views/promotion/components/components/HeaderComponent.vue')['default']
    HelpCenter: typeof import('./src/views/system/HelpCenter.vue')['default']
    HelpTip: typeof import('./src/components/UI/HelpTip.vue')['default']
    HeroComponent: typeof import('./src/views/promotion/components/components/HeroComponent.vue')['default']
    ImageUpload: typeof import('./src/components/Upload/ImageUpload.vue')['default']
    InfoPreview: typeof import('./src/components/preview/InfoPreview.vue')['default']
    LandingPageAnalytics: typeof import('./src/views/promotion/components/LandingPageAnalytics.vue')['default']
    LandingPageEditor: typeof import('./src/views/promotion/components/LandingPageEditor.vue')['default']
    LandingPagePreview: typeof import('./src/components/LandingPagePreview.vue')['default']
    LandingPages: typeof import('./src/views/promotion/LandingPages.vue')['default']
    LandingPageTemplates: typeof import('./src/components/LandingPageTemplates.vue')['default']
    LayoutDesigner: typeof import('./src/components/LayoutDesigner.vue')['default']
    LineChart: typeof import('./src/components/Charts/LineChart.vue')['default']
    LinkAnalyticsDrawer: typeof import('./src/views/promotion/components/LinkAnalyticsDrawer.vue')['default']
    LinkDialog: typeof import('./src/views/promotion/components/LinkDialog.vue')['default']
    LinkManagement: typeof import('./src/views/promotion/LinkManagement.vue')['default']
    LinkManagementAntd: typeof import('./src/views/promotion/LinkManagementAntd.vue')['default']
    LoadingPage: typeof import('./src/components/LoadingPage.vue')['default']
    Login: typeof import('./src/views/Login.vue')['default']
    MapDataManager: typeof import('./src/views/tools/MapDataManager.vue')['default']
    MediaCarousel: typeof import('./src/components/MediaCarousel.vue')['default']
    MediaUploader: typeof import('./src/components/MediaUploader.vue')['default']
    MemberDialog: typeof import('./src/views/community/components/MemberDialog.vue')['default']
    MembersPreview: typeof import('./src/components/preview/MembersPreview.vue')['default']
    MenuTest: typeof import('./src/views/system/MenuTest.vue')['default']
    MetricsGrid: typeof import('./src/components/dashboard/MetricsGrid.vue')['default']
    MigrationProgress: typeof import('./src/views/system/MigrationProgress.vue')['default']
    MigrationTest: typeof import('./src/views/system/MigrationTest.vue')['default']
    MiniLineChart: typeof import('./src/components/Charts/MiniLineChart.vue')['default']
    MobileNavigation: typeof import('./src/components/navigation/MobileNavigation.vue')['default']
    ModernDashboard: typeof import('./src/components/dashboard/ModernDashboard.vue')['default']
    ModernLayout: typeof import('./src/components/layout/ModernLayout.vue')['default']
    ModernLayoutOptimized: typeof import('./src/components/layout/ModernLayout-optimized.vue')['default']
    ModernMenuItem: typeof import('./src/components/layout/ModernMenuItem.vue')['default']
    ModernNavigationSidebar: typeof import('./src/components/navigation/ModernNavigationSidebar.vue')['default']
    ModernRichTextEditor: typeof import('./src/components/ModernRichTextEditor.vue')['default']
    ModuleItem: typeof import('./src/components/navigation/components/ModuleItem.vue')['default']
    Monitor: typeof import('./src/views/system/Monitor.vue')['default']
    MyTemplates: typeof import('./src/views/community/MyTemplates.vue')['default']
    NavigationEnhancer: typeof import('./src/components/NavigationEnhancer.vue')['default']
    NavigationExample: typeof import('./src/components/navigation/NavigationExample.vue')['default']
    NavigationGroupItem: typeof import('./src/components/navigation/NavigationGroupItem.vue')['default']
    NavigationHeader: typeof import('./src/components/navigation/NavigationHeader.vue')['default']
    NavigationMenuItem: typeof import('./src/components/navigation/NavigationMenuItem.vue')['default']
    NavigationSidebar: typeof import('./src/components/navigation/NavigationSidebar.vue')['default']
    NavigationTest: typeof import('./src/views/dev-tools/NavigationTest.vue')['default']
    NavigationTestPanel: typeof import('./src/components/NavigationTestPanel.vue')['default']
    NavigationUserPanel: typeof import('./src/components/navigation/NavigationUserPanel.vue')['default']
    NotificationCenter: typeof import('./src/components/dashboard/NotificationCenter.vue')['default']
    NotificationDrawer: typeof import('./src/components/NotificationDrawer.vue')['default']
    Notifications: typeof import('./src/views/system/Notifications.vue')['default']
    NotificationTemplateManager: typeof import('./src/views/system/components/NotificationTemplateManager.vue')['default']
    NotificationTest: typeof import('./src/views/system/NotificationTest.vue')['default']
    OperationLogs: typeof import('./src/views/system/OperationLogs.vue')['default']
    OptimizedDataScreen: typeof import('./src/views/dashboard/OptimizedDataScreen.vue')['default']
    OptimizedNavigation: typeof import('./src/components/OptimizedNavigation.vue')['default']
    OptimizedSidebar: typeof import('./src/components/layout/OptimizedSidebar.vue')['default']
    OrderAnalytics: typeof import('./src/views/orders/OrderAnalytics.vue')['default']
    OrderDetail: typeof import('./src/views/orders/OrderDetail.vue')['default']
    OrderDetailDialog: typeof import('./src/components/OrderDetailDialog.vue')['default']
    OrderList: typeof import('./src/views/orders/OrderList.vue')['default']
    OrderListAntd: typeof import('./src/views/orders/OrderListAntd.vue')['default']
    PageLayout: typeof import('./src/components/layout/PageLayout.vue')['default']
    Pagination: typeof import('./src/components/Pagination/index.vue')['default']
    PaidContentEditor: typeof import('./src/components/PaidContentEditor.vue')['default']
    PartnerHierarchy: typeof import('./src/views/partners/PartnerHierarchy.vue')['default']
    PartnerManagement: typeof import('./src/views/partners/PartnerManagement.vue')['default']
    PaymentApiConfig: typeof import('./src/views/system/PaymentApiConfig.vue')['default']
    PaymentChannelManagement: typeof import('./src/views/payment/PaymentChannelManagement.vue')['default']
    PaymentConfig: typeof import('./src/views/payment/PaymentConfig.vue')['default']
    PaymentInfoDialog: typeof import('./src/components/PaymentInfoDialog.vue')['default']
    PaymentLogs: typeof import('./src/views/payment/PaymentLogs.vue')['default']
    PaymentOrders: typeof import('./src/views/payment/PaymentOrders.vue')['default']
    PaymentOrdersAntd: typeof import('./src/views/payment/PaymentOrdersAntd.vue')['default']
    PaymentRefunds: typeof import('./src/views/payment/PaymentRefunds.vue')['default']
    PaymentSettings: typeof import('./src/views/payment/PaymentSettings.vue')['default']
    PaymentTest: typeof import('./src/views/dev-tools/PaymentTest.vue')['default']
    PerformanceMonitor: typeof import('./src/components/PerformanceMonitor.vue')['default']
    PermissionAnalytics: typeof import('./src/views/permission/PermissionAnalytics.vue')['default']
    PermissionConfigDialog: typeof import('./src/views/permission/components/PermissionConfigDialog.vue')['default']
    PermissionDemo: typeof import('./src/views/permission/PermissionDemo.vue')['default']
    PermissionDialog: typeof import('./src/views/permission/components/PermissionDialog.vue')['default']
    PermissionManagement: typeof import('./src/views/permission/PermissionManagement.vue')['default']
    PermissionManagementEnhanced: typeof import('./src/views/system/PermissionManagementEnhanced.vue')['default']
    PermissionMatrix: typeof import('./src/views/system/components/PermissionMatrix.vue')['default']
    PermissionOverview: typeof import('./src/views/permission/PermissionOverview.vue')['default']
    PermissionTest: typeof import('./src/views/dev-tools/PermissionTest.vue')['default']
    PermissionTools: typeof import('./src/views/permission/PermissionTools.vue')['default']
    PermissionTree: typeof import('./src/components/permission/PermissionTree.vue')['default']
    PermissionWorkflow: typeof import('./src/views/permission/PermissionWorkflow.vue')['default']
    PieChart: typeof import('./src/components/Charts/PieChart.vue')['default']
    PopularGroups: typeof import('./src/components/dashboard/PopularGroups.vue')['default']
    PreferenceSettings: typeof import('./src/views/user/components/PreferenceSettings.vue')['default']
    PreviewDialog: typeof import('./src/components/PreviewDialog.vue')['default']
    PreviewMode: typeof import('./src/views/PreviewMode.vue')['default']
    Profile: typeof import('./src/views/user/Profile.vue')['default']
    ProfileSettings: typeof import('./src/views/user/components/ProfileSettings.vue')['default']
    PromotionAnalytics: typeof import('./src/components/PromotionAnalytics.vue')['default']
    PromotionDetailDialog: typeof import('./src/views/promotion/components/PromotionDetailDialog.vue')['default']
    PromotionDialog: typeof import('./src/views/promotion/components/PromotionDialog.vue')['default']
    PromotionLinks: typeof import('./src/views/distributor/PromotionLinks.vue')['default']
    PromotionList: typeof import('./src/views/promotion/PromotionList.vue')['default']
    QRCodeCanvas: typeof import('./src/components/common/QRCodeCanvas.vue')['default']
    QRCodeDialog: typeof import('./src/views/community/components/QRCodeDialog.vue')['default']
    QRCodeGenerator: typeof import('./src/components/QRCodeGenerator.vue')['default']
    QRCodeTest: typeof import('./src/views/test/QRCodeTest.vue')['default']
    QuickAccessPaymentConfig: typeof import('./src/views/system/QuickAccessPaymentConfig.vue')['default']
    QuickActionBar: typeof import('./src/components/navigation/components/QuickActionBar.vue')['default']
    QuickActions: typeof import('./src/components/dashboard/QuickActions.vue')['default']
    QuickCreateWizard: typeof import('./src/components/QuickCreateWizard.vue')['default']
    QuickStats: typeof import('./src/components/dashboard/QuickStats.vue')['default']
    QuickSubstationTest: typeof import('./src/views/test/QuickSubstationTest.vue')['default']
    RealPermissionManagement: typeof import('./src/views/system/RealPermissionManagement.vue')['default']
    RealtimeAnalysis: typeof import('./src/views/analytics/RealtimeAnalysis.vue')['default']
    RealtimeChart: typeof import('./src/components/dashboard/RealtimeChart.vue')['default']
    RecentActivities: typeof import('./src/components/dashboard/RecentActivities.vue')['default']
    RecentOrders: typeof import('./src/components/dashboard/RecentOrders.vue')['default']
    RecommendationPanel: typeof import('./src/components/navigation/components/RecommendationPanel.vue')['default']
    RefundDialog: typeof import('./src/components/RefundDialog.vue')['default']
    Reports: typeof import('./src/views/dashboard/Reports.vue')['default']
    ResponsiveLayout: typeof import('./src/components/layout/ResponsiveLayout.vue')['default']
    ResponsiveTable: typeof import('./src/components/ResponsiveTable.vue')['default']
    RetentionAnalysis: typeof import('./src/views/analytics/RetentionAnalysis.vue')['default']
    RoleDialog: typeof import('./src/views/permission/components/RoleDialog.vue')['default']
    RoleManagement: typeof import('./src/views/permission/RoleManagement.vue')['default']
    RoleManager: typeof import('./src/views/system/components/RoleManager.vue')['default']
    RolePermissionConfig: typeof import('./src/views/system/components/RolePermissionConfig.vue')['default']
    RoleSwitcher: typeof import('./src/components/RoleSwitcher.vue')['default']
    RoleUsersDialog: typeof import('./src/views/permission/components/RoleUsersDialog.vue')['default']
    RouteChecker: typeof import('./src/views/RouteChecker.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    RouteTest: typeof import('./src/views/debug/RouteTest.vue')['default']
    RouteTestPage: typeof import('./src/views/community/components/RouteTestPage.vue')['default']
    RuleEditDialog: typeof import('./src/views/community/components/RuleEditDialog.vue')['default']
    ScreenTestSuite: typeof import('./src/views/dev-tools/ScreenTestSuite.vue')['default']
    ScrollTest: typeof import('./src/views/dev-tools/ScrollTest.vue')['default']
    SearchHeader: typeof import('./src/components/navigation/components/SearchHeader.vue')['default']
    SecurityManagement: typeof import('./src/views/security/SecurityManagement.vue')['default']
    SecurityReportDialog: typeof import('./src/views/system/components/SecurityReportDialog.vue')['default']
    SensitiveWordsDialog: typeof import('./src/views/community/components/SensitiveWordsDialog.vue')['default']
    Settings: typeof import('./src/views/system/Settings.vue')['default']
    ShortcutHelp: typeof import('./src/components/ShortcutHelp.vue')['default']
    ShortLinkList: typeof import('./src/views/anti-block/ShortLinkList.vue')['default']
    ShortLinkListAntd: typeof import('./src/views/anti-block/ShortLinkListAntd.vue')['default']
    Simple404: typeof import('./src/views/Simple404.vue')['default']
    SimpleDashboard: typeof import('./src/views/dashboard/SimpleDashboard.vue')['default']
    SimpleDataScreen: typeof import('./src/views/dashboard/SimpleDataScreen.vue')['default']
    SimpleGroupCreate: typeof import('./src/views/community/components/SimpleGroupCreate.vue')['default']
    SimpleGroupCreateTest: typeof import('./src/views/community/components/SimpleGroupCreateTest.vue')['default']
    SimpleGroupList: typeof import('./src/views/community/SimpleGroupList.vue')['default']
    SimpleLayout: typeof import('./src/components/layout/SimpleLayout.vue')['default']
    SimpleModernDashboard: typeof import('./src/views/dashboard/SimpleModernDashboard.vue')['default']
    SimplePaymentSettings: typeof import('./src/views/payment/SimplePaymentSettings.vue')['default']
    SimplePermissionManagement: typeof import('./src/views/system/SimplePermissionManagement.vue')['default']
    SimpleRouteTest: typeof import('./src/views/test/SimpleRouteTest.vue')['default']
    SimpleSubstationList: typeof import('./src/views/substation/SimpleSubstationList.vue')['default']
    SimpleTestDataScreen: typeof import('./src/views/dashboard/SimpleTestDataScreen.vue')['default']
    SmartBreadcrumb: typeof import('./src/components/navigation/SmartBreadcrumb.vue')['default']
    SmartCityReplacement: typeof import('./src/components/SmartCityReplacement.vue')['default']
    SocialShare: typeof import('./src/components/SocialShare.vue')['default']
    StatCard: typeof import('./src/components/dashboard/StatCard.vue')['default']
    StatsOverviewWidget: typeof import('./src/components/widgets/StatsOverviewWidget.vue')['default']
    SubstationAlerts: typeof import('./src/views/substation/SubstationAlerts.vue')['default']
    SubstationAnalytics: typeof import('./src/views/substation/SubstationAnalytics.vue')['default']
    SubstationDetail: typeof import('./src/views/substation/SubstationDetail.vue')['default']
    SubstationEditDialog: typeof import('./src/views/substation/components/SubstationEditDialog.vue')['default']
    SubstationFinance: typeof import('./src/views/substation/SubstationFinance.vue')['default']
    SubstationList: typeof import('./src/views/substation/SubstationList.vue')['default']
    SubstationMonitor: typeof import('./src/views/substation/SubstationMonitor.vue')['default']
    SubstationPermissionManager: typeof import('./src/views/system/components/SubstationPermissionManager.vue')['default']
    SubstationReports: typeof import('./src/views/substation/SubstationReports.vue')['default']
    SubstationTemplates: typeof import('./src/views/substation/SubstationTemplates.vue')['default']
    SubstationTest: typeof import('./src/views/test/SubstationTest.vue')['default']
    SystemArchitectureDoc: typeof import('./src/views/system/components/SystemArchitectureDoc.vue')['default']
    SystemIntegrationTest: typeof import('./src/views/system/SystemIntegrationTest.vue')['default']
    SystemMonitor: typeof import('./src/views/analytics/SystemMonitor.vue')['default']
    SystemPermissionConfig: typeof import('./src/views/system/components/SystemPermissionConfig.vue')['default']
    SystemStatus: typeof import('./src/components/dashboard/SystemStatus.vue')['default']
    TemplateManagement: typeof import('./src/views/community/TemplateManagement.vue')['default']
    TemplateManagementDebug: typeof import('./src/views/community/TemplateManagementDebug.vue')['default']
    TemplateManagementSimple: typeof import('./src/views/community/TemplateManagementSimple.vue')['default']
    TemplateOperationGuide: typeof import('./src/components/TemplateOperationGuide.vue')['default']
    TemplateSelector: typeof import('./src/views/promotion/components/TemplateSelector.vue')['default']
    TestNavigationSystem: typeof import('./src/views/dev-tools/TestNavigationSystem.vue')['default']
    TestPage: typeof import('./src/views/dev-tools/TestPage.vue')['default']
    TestPreview: typeof import('./src/views/dev-tools/TestPreview.vue')['default']
    TestRoute: typeof import('./src/views/dev-tools/TestRoute.vue')['default']
    TransactionList: typeof import('./src/views/finance/TransactionList.vue')['default']
    UltraDataScreen: typeof import('./src/views/dashboard/UltraDataScreen.vue')['default']
    UnifiedAnalytics: typeof import('./src/views/analytics/UnifiedAnalytics.vue')['default']
    UnifiedGroupForm: typeof import('./src/views/community/components/UnifiedGroupForm.vue')['default']
    UserAdd: typeof import('./src/views/user/UserAdd.vue')['default']
    UserAnalytics: typeof import('./src/views/user/UserAnalytics.vue')['default']
    UserBehavior: typeof import('./src/views/analytics/UserBehavior.vue')['default']
    UserCenter: typeof import('./src/views/user/UserCenter.vue')['default']
    UserDetailDrawer: typeof import('./src/views/user/components/UserDetailDrawer.vue')['default']
    UserDialog: typeof import('./src/views/user/components/UserDialog.vue')['default']
    UserGuide: typeof import('./src/views/system/UserGuide.vue')['default']
    UserList: typeof import('./src/views/user/UserList.vue')['default']
    UserListAntd: typeof import('./src/views/user/UserListAntd.vue')['default']
    UserPermissionEditor: typeof import('./src/views/system/components/UserPermissionEditor.vue')['default']
    UserPermissionManager: typeof import('./src/views/system/components/UserPermissionManager.vue')['default']
    UserProfile: typeof import('./src/views/community/UserProfile.vue')['default']
    UserSettings: typeof import('./src/views/user/UserSettings.vue')['default']
    VideoPlayer: typeof import('./src/components/VideoPlayer.vue')['default']
    VideoPreview: typeof import('./src/components/preview/VideoPreview.vue')['default']
    VideoUploader: typeof import('./src/components/VideoUploader.vue')['default']
    VirtualScrollList: typeof import('./src/components/common/VirtualScrollList.vue')['default']
    Welcome: typeof import('./src/views/Welcome.vue')['default']
    WelcomeBanner: typeof import('./src/components/dashboard/WelcomeBanner.vue')['default']
    WithdrawApply: typeof import('./src/views/finance/WithdrawApply.vue')['default']
    WithdrawManage: typeof import('./src/views/finance/WithdrawManage.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
