<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Contracts\Services\JWTServiceInterface;
use App\Contracts\Services\WechatGroupServiceInterface;
use App\Contracts\Repositories\BaseRepositoryInterface;
use App\Services\JWTService;
// use App\Services\Implementations\UnifiedWechatGroupService;
use App\Repositories\Abstracts\BaseRepository;

/**
 * 服务接口绑定提供者
 * 
 * 负责将接口与具体实现进行绑定
 * 遵循依赖倒置原则 (DIP)
 */
class ServiceInterfaceServiceProvider extends ServiceProvider
{
    /**
     * 服务接口绑定映射
     *
     * @var array
     */
    public array $bindings = [
        // 服务层接口绑定
        JWTServiceInterface::class => JWTService::class,
        // WechatGroupServiceInterface::class => UnifiedWechatGroupService::class, // 暂时禁用，等待实现完成
        
        // 仓储层接口绑定
        // BaseRepositoryInterface::class => BaseRepository::class, // 暂时禁用
    ];

    /**
     * 单例服务绑定
     *
     * @var array
     */
    public array $singletons = [
        JWTServiceInterface::class => JWTService::class,
    ];

    /**
     * 注册服务
     */
    public function register(): void
    {
        // 注册普通绑定
        foreach ($this->bindings as $abstract => $concrete) {
            $this->app->bind($abstract, $concrete);
        }

        // 注册单例绑定
        foreach ($this->singletons as $abstract => $concrete) {
            $this->app->singleton($abstract, $concrete);
        }

        // 注册条件绑定
        $this->registerConditionalBindings();
        
        // 注册扩展绑定
        $this->registerExtendedBindings();
    }

    /**
     * 启动服务
     */
    public function boot(): void
    {
        // 验证绑定的正确性
        $this->validateBindings();
    }

    /**
     * 注册条件绑定
     */
    protected function registerConditionalBindings(): void
    {
        // 根据环境变量决定JWT服务实现
        $this->app->when([
            \App\Http\Controllers\Api\AuthController::class,
            \App\Http\Middleware\JwtMiddleware::class,
        ])
        ->needs(JWTServiceInterface::class)
        ->give(function ($app) {
            // 可以根据配置返回不同的实现
            $driver = config('auth.jwt_driver', 'default');
            
            return match ($driver) {
                'redis' => $app->make(\App\Services\RedisJWTService::class),
                'cache' => $app->make(\App\Services\CacheJWTService::class),
                default => $app->make(JWTService::class),
            };
        });
    }

    /**
     * 注册扩展绑定
     */
    protected function registerExtendedBindings(): void
    {
        // 注册仓储模式的具体实现
        $this->app->bind(
            \App\Contracts\Repositories\UserRepositoryInterface::class,
            \App\Repositories\UserRepository::class
        );

        $this->app->bind(
            \App\Contracts\Repositories\WechatGroupRepositoryInterface::class,
            \App\Repositories\WechatGroupRepository::class
        );

        // 注册缓存装饰器
        // $this->app->extend(WechatGroupServiceInterface::class, function ($service, $app) {
        //     if (config('cache.services.wechat_group.enabled', false)) {
        //         return new \App\Services\Decorators\CachedWechatGroupService($service);
        //     }
        //     return $service;
        // });
    }

    /**
     * 验证绑定的正确性
     */
    protected function validateBindings(): void
    {
        if (!$this->app->environment('production')) {
            // 在非生产环境验证接口实现的正确性
            foreach ($this->bindings as $abstract => $concrete) {
                try {
                    $instance = $this->app->make($abstract);
                    
                    if (!$instance instanceof $concrete) {
                        \Log::warning("服务绑定验证失败: {$abstract} -> {$concrete}");
                    }
                } catch (\Exception $e) {
                    \Log::error("服务绑定错误: {$abstract} -> {$concrete}", [
                        'error' => $e->getMessage()
                    ]);
                }
            }
        }
    }

    /**
     * 获取提供的服务
     */
    public function provides(): array
    {
        return array_keys($this->bindings);
    }

    /**
     * 服务是否延迟加载
     */
    public function isDeferred(): bool
    {
        return true;
    }
}